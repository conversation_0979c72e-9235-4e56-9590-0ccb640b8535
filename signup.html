<!DOCTYPE html>
<html lang="fr">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inscription - PaySoft</title>
    <meta name="description"
        content="Créez votre compte PaySoft et commencez à recevoir des paiements en ligne dès aujourd'hui.">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/boxicons@2.1.4/css/boxicons.min.css">
    <link rel="stylesheet" href="style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link
        href="https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&family=Work+Sans:wght@300;400;500;600;700&display=swap"
        rel="stylesheet">
    <style>
        /* Additional styles specific to signup page */
        .auth-section {
            min-height: 100vh;
            display: flex;
            align-items: center;
            padding: 80px 0;
            background-color: #f8fafc;
        }

        .auth-card {
            background: white;
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-lg);
            overflow: hidden;
        }

        .auth-image {
            background: var(--primary-gradient);
            padding: 3rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
            color: white;
            height: 100%;
        }

        .auth-image h2 {
            color: white;
            margin-bottom: 1.5rem;
        }

        .auth-image p {
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 2rem;
        }

        .auth-features {
            margin-top: 2rem;
        }

        .auth-feature {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .auth-feature i {
            font-size: 1.5rem;
            color: white;
            background: rgba(255, 255, 255, 0.2);
            width: 40px;
            height: 40px;
            border-radius: var(--border-radius);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .auth-feature p {
            margin-bottom: 0;
        }

        .auth-form {
            padding: 3rem;
        }

        .auth-form h3 {
            margin-bottom: 0.5rem;
        }

        .auth-form p {
            margin-bottom: 2rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            font-weight: 600;
            margin-bottom: 0.5rem;
            display: block;
        }

        .form-control {
            padding: 0.75rem 1rem;
            border-radius: var(--border-radius);
            border: 1px solid var(--gray-light-color);
            font-size: 1rem;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        .password-field {
            position: relative;
        }

        .password-toggle {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            color: var(--gray-color);
        }

        .auth-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 2rem;
        }

        .auth-footer a {
            color: var(--primary-color);
            font-weight: 500;
        }

        .social-login {
            margin-top: 2rem;
            text-align: center;
        }

        .social-login p {
            margin-bottom: 1rem;
            color: var(--gray-color);
            position: relative;
        }

        .social-login p::before,
        .social-login p::after {
            content: '';
            position: absolute;
            top: 50%;
            width: 30%;
            height: 1px;
            background-color: var(--gray-light-color);
        }

        .social-login p::before {
            left: 0;
        }

        .social-login p::after {
            right: 0;
        }

        .social-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
        }

        .social-button {
            width: 50px;
            height: 50px;
            border-radius: var(--border-radius);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            transition: all 0.3s ease;
        }

        .social-button:hover {
            transform: translateY(-3px);
        }

        .google {
            background-color: #DB4437;
        }

        .facebook {
            background-color: #4267B2;
        }

        .apple {
            background-color: #000000;
        }

        .form-row {
            display: flex;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .form-row .form-group {
            flex: 1;
            margin-bottom: 0;
        }

        .terms-check {
            margin-bottom: 1.5rem;
        }

        .terms-check a {
            color: var(--primary-color);
            font-weight: 500;
        }

        @media (max-width: 991.98px) {
            .auth-image {
                padding: 2rem;
                text-align: center;
            }

            .auth-feature {
                justify-content: center;
            }

            .auth-form {
                padding: 2rem;
            }

            .form-row {
                flex-direction: column;
                gap: 1.5rem;
            }
        }
    </style>
</head>

<body>

    <!-- Signup Section -->
    <section class="auth-section">
        <div class="container">
            <div class="row g-0 auth-card">
                <div class="col-lg-6 d-none d-lg-block">
                    <div class="auth-image">
                        <h2>Rejoignez des milliers d'entrepreneurs africains</h2>
                        <p>Créez votre compte PaySoft et commencez à recevoir des paiements en ligne en quelques
                            minutes.</p>

                        <div class="auth-features">
                            <div class="auth-feature">
                                <i class='bx bx-check-shield'></i>
                                <div>
                                    <h5>Sécurité de niveau bancaire</h5>
                                    <p>Vos données et transactions sont protégées</p>
                                </div>
                            </div>
                            <div class="auth-feature">
                                <i class='bx bx-wallet'></i>
                                <div>
                                    <h5>Paiements instantanés</h5>
                                    <p>Recevez vos fonds rapidement sur votre compte</p>
                                </div>
                            </div>
                            <div class="auth-feature">
                                <i class='bx bx-support'></i>
                                <div>
                                    <h5>Support 24/7</h5>
                                    <p>Notre équipe est disponible pour vous aider</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="auth-form">
                        <a href="index.html" class="navbar-brand mb-4 d-inline-block">
                            <span class="logo-text">Pay<span class="text-gradient">Soft</span></span>
                        </a>
                        <h3>Créez votre compte</h3>
                        <p>Commencez votre essai gratuit de 14 jours, aucune carte bancaire requise</p>

                        <form action="dashboard.html" method="GET" class="needs-validation" novalidate>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="firstName" class="form-label">Prénom</label>
                                    <input type="text" class="form-control" id="firstName" placeholder="Votre prénom"
                                        required>
                                    <div class="invalid-feedback">
                                        Veuillez entrer votre prénom.
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="lastName" class="form-label">Nom</label>
                                    <input type="text" class="form-control" id="lastName" placeholder="Votre nom"
                                        required>
                                    <div class="invalid-feedback">
                                        Veuillez entrer votre nom.
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="businessName" class="form-label">Nom de l'entreprise</label>
                                <input type="text" class="form-control" id="businessName"
                                    placeholder="Nom de votre entreprise">
                            </div>

                            <div class="form-group">
                                <label for="email" class="form-label">Email professionnel</label>
                                <input type="email" class="form-control" id="email" placeholder="<EMAIL>"
                                    required>
                                <div class="invalid-feedback">
                                    Veuillez entrer une adresse email valide.
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="phone" class="form-label">Numéro de téléphone</label>
                                <input type="tel" class="form-control" id="phone" placeholder="+XXX XXXXXXXXX" required>
                                <div class="invalid-feedback">
                                    Veuillez entrer un numéro de téléphone valide.
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="password" class="form-label">Mot de passe</label>
                                <div class="password-field">
                                    <input type="password" class="form-control" id="password"
                                        placeholder="8+ caractères, 1 majuscule, 1 chiffre" required>
                                    <span class="password-toggle" id="passwordToggle">
                                        <i class='bx bx-hide'></i>
                                    </span>
                                </div>
                                <div class="invalid-feedback">
                                    Votre mot de passe doit contenir au moins 8 caractères.
                                </div>
                            </div>

                            <div class="form-check terms-check">
                                <input class="form-check-input" type="checkbox" id="termsCheck" required>
                                <label class="form-check-label" for="termsCheck">
                                    J'accepte les <a href="#">Conditions d'utilisation</a> et la <a href="#">Politique
                                        de confidentialité</a>
                                </label>
                                <div class="invalid-feedback">
                                    Vous devez accepter les conditions pour continuer.
                                </div>
                            </div>

                            <button type="submit" class="btn btn-primary w-100">Créer mon compte</button>
                        </form>

                        <div class="social-login">
                            <p>Ou inscrivez-vous avec</p>
                            <div class="social-buttons">
                                <a href="#" class="social-button google">
                                    <i class='bx bxl-google'></i>
                                </a>
                                <a href="#" class="social-button facebook">
                                    <i class='bx bxl-facebook'></i>
                                </a>
                                <a href="#" class="social-button apple">
                                    <i class='bx bxl-apple'></i>
                                </a>
                            </div>
                        </div>

                        <div class="text-center mt-4">
                            <p class="mb-0">Vous avez déjà un compte? <a href="login.html">Connectez-vous</a></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Password visibility toggle
        const passwordToggle = document.getElementById('passwordToggle');
        const passwordField = document.getElementById('password');

        if (passwordToggle && passwordField) {
            passwordToggle.addEventListener('click', function () {
                const type = passwordField.getAttribute('type') === 'password' ? 'text' : 'password';
                passwordField.setAttribute('type', type);

                // Toggle icon
                const icon = this.querySelector('i');
                icon.classList.toggle('bx-hide');
                icon.classList.toggle('bx-show');
            });
        }

        // Form validation
        const forms = document.querySelectorAll('.needs-validation');
        if (forms.length > 0) {
            Array.from(forms).forEach(form => {
                form.addEventListener('submit', event => {
                    if (!form.checkValidity()) {
                        event.preventDefault();
                        event.stopPropagation();
                    }
                    form.classList.add('was-validated');
                }, false);
            });
        }

        // Sticky Header
        const header = document.querySelector('.navbar');
        window.addEventListener('scroll', function () {
            if (window.scrollY > 100) {
                header.classList.add('sticky');
            } else {
                header.classList.remove('sticky');
            }
        });

        // Add sticky class on page load since we're not at the top
        window.addEventListener('DOMContentLoaded', function () {
            header.classList.add('sticky');
        });
    </script>
</body>

</html>
/* ===== INDEX-SPECIFIC STYLES ===== */

/* Custom animations for index page */
@keyframes float {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-15px);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes gradient {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

/* Hero section specific styles */
.hero-gradient-bg {
    background: linear-gradient(-45deg, #f8fafc, #ffffff, #f0f9ff, #f0fdf4);
    background-size: 400% 400%;
    animation: gradient 15s ease infinite;
}

/* Floating elements */
.floating-element {
    animation: float 6s ease-in-out infinite;
}

.floating-element:nth-child(2) {
    animation-delay: 2s;
}

.floating-element:nth-child(3) {
    animation-delay: 4s;
}

/* Custom scrollbar for modern look */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(to bottom, #0ea5e9, #22c55e);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(to bottom, #0284c7, #16a34a);
}

/* Smooth transitions for all interactive elements */
* {
    transition: all 0.3s ease;
}

/* Custom button hover effects */
.btn-gradient {
    background: linear-gradient(135deg, #0ea5e9, #22c55e);
    background-size: 200% 200%;
    transition: all 0.3s ease;
}

.btn-gradient:hover {
    background-position: right center;
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(14, 165, 233, 0.3);
}

/* Glass morphism effect */
.glass {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
}

/* Modern card styles */
.modern-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
}

.modern-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Gradient text effect */
.gradient-text {
    background: linear-gradient(135deg, #0ea5e9, #22c55e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Mobile menu animations */
.mobile-menu-enter {
    transform: translateX(100%);
}

.mobile-menu-enter-active {
    transform: translateX(0);
    transition: transform 0.3s ease-in-out;
}

.mobile-menu-exit {
    transform: translateX(0);
}

.mobile-menu-exit-active {
    transform: translateX(100%);
    transition: transform 0.3s ease-in-out;
}

/* Responsive typography */
@media (max-width: 640px) {
    .hero-title {
        font-size: 2.5rem;
        line-height: 1.2;
    }
    
    .hero-subtitle {
        font-size: 1.125rem;
    }
}

@media (min-width: 641px) and (max-width: 1024px) {
    .hero-title {
        font-size: 3.5rem;
        line-height: 1.1;
    }
    
    .hero-subtitle {
        font-size: 1.25rem;
    }
}

@media (min-width: 1025px) {
    .hero-title {
        font-size: 4rem;
        line-height: 1.1;
    }
    
    .hero-subtitle {
        font-size: 1.375rem;
    }
}

/* Custom focus styles for accessibility */
.focus-ring:focus {
    outline: 2px solid #0ea5e9;
    outline-offset: 2px;
}

/* Loading animation */
.loading-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Intersection observer animations */
.fade-in-up {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s ease;
}

.fade-in-up.animate {
    opacity: 1;
    transform: translateY(0);
}

.fade-in-left {
    opacity: 0;
    transform: translateX(-30px);
    transition: all 0.8s ease;
}

.fade-in-left.animate {
    opacity: 1;
    transform: translateX(0);
}

.fade-in-right {
    opacity: 0;
    transform: translateX(30px);
    transition: all 0.8s ease;
}

.fade-in-right.animate {
    opacity: 1;
    transform: translateX(0);
}

/* Modern pricing cards */
.pricing-card {
    position: relative;
    overflow: hidden;
}

.pricing-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.pricing-card:hover::before {
    left: 100%;
}

/* Testimonial cards modern style */
.testimonial-card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 1px solid rgba(229, 231, 235, 0.5);
}

.testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    border-color: #0ea5e9;
}

/* FAQ accordion modern styling */
.faq-item {
    border-radius: 12px;
    overflow: hidden;
    margin-bottom: 1rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.faq-header {
    background: white;
    padding: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.faq-header:hover {
    background: #f8fafc;
}

.faq-content {
    background: #f8fafc;
    padding: 0 1.5rem;
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s ease;
}

.faq-content.active {
    padding: 1.5rem;
    max-height: 200px;
}

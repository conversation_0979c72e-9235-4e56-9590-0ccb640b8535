<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clients | PaySoft - Plateforme de paiement panafricaine</title>
    <meta name="description" content="Gérez vos clients et suivez leurs activités sur PaySoft, la plateforme de paiement panafricaine.">
    
    <!-- CDN Links -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        /* General Styles */
        :root {
            --primary-color: #6366f1;
            --primary-dark-color: #4f46e5;
            --secondary-color: #f59e0b;
            --dark-color: #111827;
            --gray-color: #6b7280;
            --gray-light-color: #e5e7eb;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --white-color: #ffffff;
            --border-radius: 0.5rem;
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f9fafb;
            color: var(--dark-color);
            line-height: 1.6;
        }
        
        /* Typography */
        h1, h2, h3, h4, h5, h6 {
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .text-gradient {
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }
        
        /* Shadows and Border Radius */
        .card {
            border-radius: var(--border-radius);
            border: none;
            box-shadow: var(--shadow);
            margin-bottom: 1.5rem;
        }
        
        /* Buttons */
        .btn {
            border-radius: var(--border-radius);
            padding: 0.5rem 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .btn-primary:hover {
            background-color: var(--primary-dark-color);
            border-color: var(--primary-dark-color);
        }
        
        .btn-outline-primary {
            color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .btn-outline-primary:hover {
            background-color: var(--primary-color);
            color: white;
        }
        
        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }
        
        .btn-icon {
            width: 2rem;
            height: 2rem;
            padding: 0;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background-color: rgba(99, 102, 241, 0.1);
            color: var(--primary-color);
            border: none;
        }
        
        .btn-icon:hover {
            background-color: rgba(99, 102, 241, 0.2);
        }
        
        /* Dashboard Layout */
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            width: 250px;
            height: 100vh;
            background-color: var(--white-color);
            box-shadow: var(--shadow);
            z-index: 1000;
            transition: transform 0.3s ease;
        }
        
        .sidebar-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1.5rem 1rem;
            border-bottom: 1px solid var(--gray-light-color);
        }
        
        .sidebar-close {
            display: none;
            background: none;
            border: none;
            font-size: 1.5rem;
            color: var(--gray-color);
            cursor: pointer;
        }
        
        .sidebar-user {
            display: flex;
            align-items: center;
            padding: 1rem;
            border-bottom: 1px solid var(--gray-light-color);
        }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            overflow: hidden;
            margin-right: 0.75rem;
        }
        
        .user-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .user-name {
            font-size: 0.875rem;
            font-weight: 600;
            margin-bottom: 0;
        }
        
        .user-business {
            font-size: 0.75rem;
            color: var(--gray-color);
            margin-bottom: 0;
        }
        
        .sidebar-nav {
            padding: 1rem 0;
        }
        
        .nav-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .nav-item {
            margin-bottom: 0.25rem;
        }
        
        .nav-link {
            display: flex;
            align-items: center;
            padding: 0.75rem 1.5rem;
            color: var(--gray-color);
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }
        
        .nav-link i {
            margin-right: 0.75rem;
            font-size: 1.25rem;
        }
        
        .nav-link:hover {
            color: var(--primary-color);
            background-color: rgba(99, 102, 241, 0.1);
        }
        
        .nav-item.active .nav-link {
            color: var(--primary-color);
            background-color: rgba(99, 102, 241, 0.1);
            border-left-color: var(--primary-color);
        }
        
        .sidebar-footer {
            padding: 1rem 1.5rem;
            border-top: 1px solid var(--gray-light-color);
            position: absolute;
            bottom: 0;
            width: 100%;
        }
        
        .logout-link {
            display: flex;
            align-items: center;
            color: var(--gray-color);
            text-decoration: none;
        }
        
        .logout-link i {
            margin-right: 0.75rem;
        }
        
        .logout-link:hover {
            color: var(--danger-color);
        }
        
        .main-content {
            margin-left: 250px;
            padding: 2rem;
            min-height: 100vh;
        }
        
        /* Page Header */
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }
        
        .page-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.25rem;
        }
        
        .page-subtitle {
            color: var(--gray-color);
            margin-bottom: 0;
        }
        
        .page-actions {
            display: flex;
            gap: 0.5rem;
        }
        
        /* Filter and Search */
        .filter-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }
        
        .search-box {
            position: relative;
            max-width: 300px;
            width: 100%;
        }
        
        .search-box i {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--gray-color);
        }
        
        .search-box input {
            padding-left: 2.5rem;
            border-radius: var(--border-radius);
            border: 1px solid var(--gray-light-color);
        }
        
        .filters {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }
        
        .filter-item {
            min-width: 150px;
        }
        
        /* Customer Table */
        .table {
            margin-bottom: 0;
        }
        
        .table th {
            font-weight: 600;
            color: var(--gray-color);
            border-bottom-width: 1px;
            padding-top: 1rem;
            padding-bottom: 1rem;
        }
        
        .table td {
            padding-top: 1rem;
            padding-bottom: 1rem;
            vertical-align: middle;
        }
        
        /* Status Badges */
        .badge {
            padding: 0.35rem 0.65rem;
            font-size: 0.75rem;
            font-weight: 500;
            border-radius: 50rem;
        }
        
        .badge-success {
            background-color: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
        }
        
        .badge-warning {
            background-color: rgba(245, 158, 11, 0.1);
            color: var(--warning-color);
        }
        
        .badge-danger {
            background-color: rgba(239, 68, 68, 0.1);
            color: var(--danger-color);
        }
        
        .badge-primary {
            background-color: rgba(99, 102, 241, 0.1);
            color: var(--primary-color);
        }
        
        /* Customer Details */
        .customer-icon {
            width: 40px;
            height: 40px;
            border-radius: var(--border-radius);
            background-color: rgba(99, 102, 241, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-color);
            font-size: 1.25rem;
        }
        
        /* Pagination */
        .pagination-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 1.5rem;
        }
        
        .pagination-info {
            font-size: 0.875rem;
            color: var(--gray-color);
        }
        
        .pagination {
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }
        
        .page-item {
            list-style: none;
        }
        
        .page-link {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 2.5rem;
            height: 2.5rem;
            border-radius: var(--border-radius);
            border: 1px solid var(--gray-light-color);
            color: var(--dark-color);
            font-size: 0.875rem;
            transition: all 0.3s ease;
        }
        
        .page-link:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
        }
        
        .page-item.active .page-link {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }
        
        .page-item.disabled .page-link {
            color: var(--gray-color);
            pointer-events: none;
        }
        
        /* Mobile Header (visible only on small screens) */
        .mobile-header {
            display: none;
        }
        
        /* Overlay for sidebar on mobile */
        .sidebar-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 999;
            display: none;
        }
        
        .sidebar-overlay.show {
            display: block;
        }
        
        /* Responsive */
        @media (max-width: 991.98px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0;
            }
            
            .sidebar-toggle {
                display: block;
            }
            
            .mobile-header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 1rem;
                background-color: var(--white-color);
                box-shadow: var(--shadow);
                position: sticky;
                top: 0;
                z-index: 999;
            }
            
            .filter-row {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .search-box {
                max-width: 100%;
            }
        }
        
        @media (max-width: 767.98px) {
            .page-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 1rem;
            }
            
            .pagination-container {
                flex-direction: column;
                gap: 1rem;
                align-items: flex-start;
            }
        }
    </style>
</head>
<body>
    <!-- Mobile Header (visible only on small screens) -->
    <div class="mobile-header">
        <button class="sidebar-toggle" id="sidebarToggle">
            <i class='bx bx-menu'></i>
        </button>
        <a href="index.html" class="navbar-brand">
            <span class="logo-text">Pay<span class="text-gradient">Soft</span></span>
        </a>
        <div class="dropdown">
            <button class="dropdown-toggle" type="button" id="mobileUserMenu" data-bs-toggle="dropdown" aria-expanded="false">
                <i class='bx bx-user-circle' style="font-size: 1.5rem;"></i>
            </button>
            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="mobileUserMenu">
                <li><a class="dropdown-item" href="profile.html">Mon profil</a></li>
                <li><a class="dropdown-item" href="settings.html">Paramètres</a></li>
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item" href="index.html">Déconnexion</a></li>
            </ul>
        </div>
    </div>

    <!-- Sidebar Overlay -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>

    <!-- Sidebar -->
    <aside class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <a href="index.html" class="navbar-brand">
                <span class="logo-text">Pay<span class="text-gradient">Soft</span></span>
            </a>
            <button class="sidebar-close" id="sidebarClose">
                <i class='bx bx-x'></i>
            </button>
        </div>
        <div class="sidebar-user">
            <div class="user-avatar">
                <img src="https://ui-avatars.com/api/?name=John+Doe&background=6366f1&color=fff" alt="John Doe">
            </div>
            <div class="user-info">
                <h5 class="user-name">John Doe</h5>
                <p class="user-business">Acme Inc.</p>
            </div>
        </div>
        <nav class="sidebar-nav">
            <ul class="nav-list">
                <li class="nav-item">
                    <a href="dashboard.html" class="nav-link">
                        <i class='bx bxs-dashboard'></i>
                        <span>Tableau de bord</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="create-payment.html" class="nav-link">
                        <i class='bx bx-plus-circle'></i>
                        <span>Créer un paiement</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="payment-links.html" class="nav-link">
                        <i class='bx bx-link'></i>
                        <span>Liens de paiement</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="transactions.html" class="nav-link">
                        <i class='bx bx-transfer'></i>
                        <span>Transactions</span>
                    </a>
                </li>
                <li class="nav-item active">
                    <a href="customers.html" class="nav-link">
                        <i class='bx bx-user'></i>
                        <span>Clients</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="settings.html" class="nav-link">
                        <i class='bx bx-cog'></i>
                        <span>Paramètres</span>
                    </a>
                </li>
            </ul>
        </nav>
        <div class="sidebar-footer">
            <a href="index.html" class="logout-link">
                <i class='bx bx-log-out'></i>
                <span>Déconnexion</span>
            </a>
        </div>
    </aside>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Page Header -->
        <div class="page-header">
            <div>
                <h1 class="page-title">Clients</h1>
                <p class="page-subtitle">Gérez et suivez vos clients</p>
            </div>
            <div class="page-actions">
                <button class="btn btn-outline-primary me-2">
                    <i class='bx bx-export'></i> Exporter
                </button>
                <button class="btn btn-primary">
                    <i class='bx bx-plus'></i> Ajouter un client
                </button>
            </div>
        </div>

        <!-- Filters and Search -->
        <div class="card">
            <div class="card-body">
                <div class="filter-row">
                    <div class="search-box">
                        <i class='bx bx-search'></i>
                        <input type="text" class="form-control" placeholder="Rechercher un client...">
                    </div>
                    <div class="filters">
                        <div class="filter-item">
                            <select class="form-select">
                                <option value="">Statut</option>
                                <option value="active">Actif</option>
                                <option value="inactive">Inactif</option>
                            </select>
                        </div>
                        <div class="filter-item">
                            <select class="form-select">
                                <option value="">Pays</option>
                                <option value="senegal">Sénégal</option>
                                <option value="cote_ivoire">Côte d'Ivoire</option>
                                <option value="cameroun">Cameroun</option>
                                <option value="ghana">Ghana</option>
                                <option value="nigeria">Nigeria</option>
                            </select>
                        </div>
                        <div class="filter-item">
                            <input type="date" class="form-control" placeholder="Date d'inscription">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Customers Table -->
        <div class="card mt-4">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Client</th>
                                <th>Email</th>
                                <th>Téléphone</th>
                                <th>Pays</th>
                                <th>Transactions</th>
                                <th>Montant total</th>
                                <th>Statut</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="customer-icon">
                                            <i class='bx bx-user'></i>
                                        </div>
                                        <div class="ms-3">
                                            <h6 class="mb-0">Sophie Martin</h6>
                                            <small>Inscrit le 15 juin 2023</small>
                                        </div>
                                    </div>
                                </td>
                                <td><EMAIL></td>
                                <td>+221 77 123 4567</td>
                                <td>Sénégal</td>
                                <td>12</td>
                                <td>150 000 XOF</td>
                                <td>
                                    <span class="badge badge-success">Actif</span>
                                </td>
                                <td>
                                    <div class="d-flex gap-2">
                                        <button class="btn btn-sm btn-icon" title="Voir les détails">
                                            <i class='bx bx-show'></i>
                                        </button>
                                        <button class="btn btn-sm btn-icon" title="Modifier">
                                            <i class='bx bx-edit'></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="customer-icon">
                                            <i class='bx bx-user'></i>
                                        </div>
                                        <div class="ms-3">
                                            <h6 class="mb-0">Amadou Diallo</h6>
                                            <small>Inscrit le 10 juin 2023</small>
                                        </div>
                                    </div>
                                </td>
                                <td><EMAIL></td>
                                <td>+221 76 987 6543</td>
                                <td>Sénégal</td>
                                <td>8</td>
                                <td>95 000 XOF</td>
                                <td>
                                    <span class="badge badge-success">Actif</span>
                                </td>
                                <td>
                                    <div class="d-flex gap-2">
                                        <button class="btn btn-sm btn-icon" title="Voir les détails">
                                            <i class='bx bx-show'></i>
                                        </button>
                                        <button class="btn btn-sm btn-icon" title="Modifier">
                                            <i class='bx bx-edit'></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="customer-icon">
                                            <i class='bx bx-user'></i>
                                        </div>
                                        <div class="ms-3">
                                            <h6 class="mb-0">Fatou Ndiaye</h6>
                                            <small>Inscrit le 5 juin 2023</small>
                                        </div>
                                    </div>
                                </td>
                                <td><EMAIL></td>
                                <td>+221 70 456 7890</td>
                                <td>Sénégal</td>
                                <td>5</td>
                                <td>75 000 XOF</td>
                                <td>
                                    <span class="badge badge-success">Actif</span>
                                </td>
                                <td>
                                    <div class="d-flex gap-2">
                                        <button class="btn btn-sm btn-icon" title="Voir les détails">
                                            <i class='bx bx-show'></i>
                                        </button>
                                        <button class="btn btn-sm btn-icon" title="Modifier">
                                            <i class='bx bx-edit'></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="customer-icon">
                                            <i class='bx bx-user'></i>
                                        </div>
                                        <div class="ms-3">
                                            <h6 class="mb-0">Kofi Mensah</h6>
                                            <small>Inscrit le 1 juin 2023</small>
                                        </div>
                                    </div>
                                </td>
                                <td><EMAIL></td>
                                <td>+233 24 123 4567</td>
                                <td>Ghana</td>
                                <td>3</td>
                                <td>500 GHS</td>
                                <td>
                                    <span class="badge badge-success">Actif</span>
                                </td>
                                <td>
                                    <div class="d-flex gap-2">
                                        <button class="btn btn-sm btn-icon" title="Voir les détails">
                                            <i class='bx bx-show'></i>
                                        </button>
                                        <button class="btn btn-sm btn-icon" title="Modifier">
                                            <i class='bx bx-edit'></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="customer-icon">
                                            <i class='bx bx-user'></i>
                                        </div>
                                        <div class="ms-3">
                                            <h6 class="mb-0">Aya Touré</h6>
                                            <small>Inscrit le 28 mai 2023</small>
                                        </div>
                                    </div>
                                </td>
                                <td><EMAIL></td>
                                <td>+225 07 123 4567</td>
                                <td>Côte d'Ivoire</td>
                                <td>7</td>
                                <td>85 000 XOF</td>
                                <td>
                                    <span class="badge badge-warning">Inactif</span>
                                </td>
                                <td>
                                    <div class="d-flex gap-2">
                                        <button class="btn btn-sm btn-icon" title="Voir les détails">
                                            <i class='bx bx-show'></i>
                                        </button>
                                        <button class="btn btn-sm btn-icon" title="Modifier">
                                            <i class='bx bx-edit'></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="pagination-container">
                    <div class="pagination-info">
                        Affichage de 1 à 5 sur 20 clients
                    </div>
                    <nav aria-label="Page navigation">
                        <ul class="pagination">
                            <li class="page-item disabled">
                                <a class="page-link" href="#" aria-label="Previous">
                                    <i class='bx bx-chevron-left'></i>
                                </a>
                            </li>
                            <li class="page-item active"><a class="page-link" href="#">1</a></li>
                            <li class="page-item"><a class="page-link" href="#">2</a></li>
                            <li class="page-item"><a class="page-link" href="#">3</a></li>
                            <li class="page-item"><a class="page-link" href="#">4</a></li>
                            <li class="page-item">
                                <a class="page-link" href="#" aria-label="Next">
                                    <i class='bx bx-chevron-right'></i>
                                </a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </main>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Toggle Sidebar
        const sidebar = document.getElementById('sidebar');
        const sidebarToggle = document.getElementById('sidebarToggle');
        const sidebarClose = document.getElementById('sidebarClose');
        const sidebarOverlay = document.getElementById('sidebarOverlay');

        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', () => {
                sidebar.classList.toggle('show');
                sidebarOverlay.classList.toggle('show');
            });
        }

        if (sidebarClose) {
            sidebarClose.addEventListener('click', () => {
                sidebar.classList.remove('show');
                sidebarOverlay.classList.remove('show');
            });
        }

        if (sidebarOverlay) {
            sidebarOverlay.addEventListener('click', () => {
                sidebar.classList.remove('show');
                sidebarOverlay.classList.remove('show');
            });
        }

        // Search functionality
        const searchInput = document.querySelector('.search-box input');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                // Implement search functionality here
                console.log('Searching for:', e.target.value);
            });
        }

        // Filter functionality
        const filterSelects = document.querySelectorAll('.filter-item select, .filter-item input');
        filterSelects.forEach(select => {
            select.addEventListener('change', (e) => {
                // Implement filter functionality here
                console.log('Filter changed:', e.target.value);
            });
        });
    </script>
</body>
</html>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Connexion - PaySoft</title>
    <meta name="description" content="Connectez-vous à votre compte PaySoft pour gérer vos paiements et factures.">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/boxicons@2.1.4/css/boxicons.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&family=Work+Sans:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* Additional styles specific to login page */
        .auth-section {
            min-height: 100vh;
            display: flex;
            align-items: center;
            padding: 80px 0;
            background-color: #f8fafc;
        }
        
        .auth-card {
            background: white;
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-lg);
            overflow: hidden;
        }
        
        .auth-image {
            background: var(--primary-gradient);
            padding: 3rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
            color: white;
            height: 100%;
        }
        
        .auth-image h2 {
            color: white;
            margin-bottom: 1.5rem;
        }
        
        .auth-image p {
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 2rem;
        }
        
        .auth-features {
            margin-top: 2rem;
        }
        
        .auth-feature {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }
        
        .auth-feature i {
            font-size: 1.5rem;
            color: white;
            background: rgba(255, 255, 255, 0.2);
            width: 40px;
            height: 40px;
            border-radius: var(--border-radius);
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .auth-feature p {
            margin-bottom: 0;
        }
        
        .auth-form {
            padding: 3rem;
        }
        
        .auth-form h3 {
            margin-bottom: 0.5rem;
        }
        
        .auth-form p {
            margin-bottom: 2rem;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-label {
            font-weight: 600;
            margin-bottom: 0.5rem;
            display: block;
        }
        
        .form-control {
            padding: 0.75rem 1rem;
            border-radius: var(--border-radius);
            border: 1px solid var(--gray-light-color);
            font-size: 1rem;
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }
        
        .password-field {
            position: relative;
        }
        
        .password-toggle {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            color: var(--gray-color);
        }
        
        .auth-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 2rem;
        }
        
        .auth-footer a {
            color: var(--primary-color);
            font-weight: 500;
        }
        
        .social-login {
            margin-top: 2rem;
            text-align: center;
        }
        
        .social-login p {
            margin-bottom: 1rem;
            color: var(--gray-color);
            position: relative;
        }
        
        .social-login p::before,
        .social-login p::after {
            content: '';
            position: absolute;
            top: 50%;
            width: 30%;
            height: 1px;
            background-color: var(--gray-light-color);
        }
        
        .social-login p::before {
            left: 0;
        }
        
        .social-login p::after {
            right: 0;
        }
        
        .social-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
        }
        
        .social-button {
            width: 50px;
            height: 50px;
            border-radius: var(--border-radius);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            transition: all 0.3s ease;
        }
        
        .social-button:hover {
            transform: translateY(-3px);
        }
        
        .google {
            background-color: #DB4437;
        }
        
        .facebook {
            background-color: #4267B2;
        }
        
        .apple {
            background-color: #000000;
        }
        
        @media (max-width: 991.98px) {
            .auth-image {
                padding: 2rem;
                text-align: center;
            }
            
            .auth-feature {
                justify-content: center;
            }
            
            .auth-form {
                padding: 2rem;
            }
        }
    </style>
</head>
<body>
    

    <!-- Login Section -->
    <section class="auth-section">
        <div class="container">
            <div class="row g-0 auth-card">
                <div class="col-lg-6">
                    <div class="auth-form">
                        <a href="index.html" class="navbar-brand mb-4 d-inline-block">
                            <span class="logo-text">Pay<span class="text-gradient">Soft</span></span>
                        </a>
                        <h3>Bon retour parmi nous</h3>
                        <p>Connectez-vous pour accéder à votre tableau de bord</p>
                        
                        <form action="dashboard.html" method="GET" class="needs-validation" novalidate>
                            <div class="form-group">
                                <label for="email" class="form-label">Email</label>
                                <input type="email" class="form-control" id="email" placeholder="<EMAIL>" required>
                                <div class="invalid-feedback">
                                    Veuillez entrer une adresse email valide.
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <div class="d-flex justify-content-between">
                                    <label for="password" class="form-label">Mot de passe</label>
                                    <a href="forgot-password.html" class="text-sm">Mot de passe oublié?</a>
                                </div>
                                <div class="password-field">
                                    <input type="password" class="form-control" id="password" placeholder="••••••••" required>
                                    <span class="password-toggle" id="passwordToggle">
                                        <i class='bx bx-hide'></i>
                                    </span>
                                </div>
                                <div class="invalid-feedback">
                                    Veuillez entrer votre mot de passe.
                                </div>
                            </div>
                            
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="rememberMe">
                                <label class="form-check-label" for="rememberMe">
                                    Se souvenir de moi
                                </label>
                            </div>
                            
                            <button type="submit" class="btn btn-primary w-100">Se connecter</button>
                        </form>
                        
                        <div class="social-login">
                            <p>Ou connectez-vous avec</p>
                            <div class="social-buttons">
                                <a href="#" class="social-button google">
                                    <i class='bx bxl-google'></i>
                                </a>
                                <a href="#" class="social-button facebook">
                                    <i class='bx bxl-facebook'></i>
                                </a>
                                <a href="#" class="social-button apple">
                                    <i class='bx bxl-apple'></i>
                                </a>
                            </div>
                        </div>
                        
                        <div class="text-center mt-4">
                            <p class="mb-0">Vous n'avez pas de compte? <a href="signup.html">Inscrivez-vous</a></p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 d-none d-lg-block">
                    <div class="auth-image">
                        <h2>Gérez vos paiements en toute simplicité</h2>
                        <p>Accédez à votre tableau de bord pour suivre vos revenus, créer de nouvelles pages de paiement et gérer vos clients.</p>
                        
                        <div class="auth-features">
                            <div class="auth-feature">
                                <i class='bx bx-line-chart'></i>
                                <div>
                                    <h5>Suivi en temps réel</h5>
                                    <p>Visualisez vos revenus et transactions instantanément</p>
                                </div>
                            </div>
                            <div class="auth-feature">
                                <i class='bx bx-link'></i>
                                <div>
                                    <h5>Liens de paiement</h5>
                                    <p>Créez et partagez des liens de paiement en quelques clics</p>
                                </div>
                            </div>
                            <div class="auth-feature">
                                <i class='bx bx-bell'></i>
                                <div>
                                    <h5>Notifications</h5>
                                    <p>Recevez des alertes pour chaque nouvelle transaction</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Password visibility toggle
        const passwordToggle = document.getElementById('passwordToggle');
        const passwordField = document.getElementById('password');
        
        if (passwordToggle && passwordField) {
            passwordToggle.addEventListener('click', function() {
                const type = passwordField.getAttribute('type') === 'password' ? 'text' : 'password';
                passwordField.setAttribute('type', type);
                
                // Toggle icon
                const icon = this.querySelector('i');
                icon.classList.toggle('bx-hide');
                icon.classList.toggle('bx-show');
            });
        }
        
        // Form validation
        const forms = document.querySelectorAll('.needs-validation');
        if (forms.length > 0) {
            Array.from(forms).forEach(form => {
                form.addEventListener('submit', event => {
                    if (!form.checkValidity()) {
                        event.preventDefault();
                        event.stopPropagation();
                    }
                    form.classList.add('was-validated');
                }, false);
            });
        }
        
        // Sticky Header
        const header = document.querySelector('.navbar');
        window.addEventListener('scroll', function() {
            if (window.scrollY > 100) {
                header.classList.add('sticky');
            } else {
                header.classList.remove('sticky');
            }
        });
        
        // Add sticky class on page load since we're not at the top
        window.addEventListener('DOMContentLoaded', function() {
            header.classList.add('sticky');
        });
    </script>
</body>
</html>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tableau de Bord - PaySoft</title>
    <meta name="description" content="Gérez vos paiements, factures et clients depuis votre tableau de bord PaySoft.">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/boxicons@2.1.4/css/boxicons.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/apexcharts@3.35.0/dist/apexcharts.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&family=Work+Sans:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #6366f1;
            --secondary-color: #4f46e5;
            --accent-color: #8b5cf6;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --info-color: #3b82f6;
            --dark-color: #1e293b;
            --gray-color: #64748b;
            --gray-light-color: #e2e8f0;
            --body-color: #f8fafc;
            --white-color: #ffffff;
            
            --primary-gradient: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
            
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
            
            --border-radius: 0.375rem;
            --border-radius-md: 0.5rem;
            --border-radius-lg: 0.75rem;
            --border-radius-xl: 1rem;
            --border-radius-2xl: 1.5rem;
            --border-radius-full: 9999px;
            
            --font-sans: 'Work Sans', sans-serif;
            --font-heading: 'Space Grotesk', sans-serif;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: var(--font-sans);
            color: var(--dark-color);
            background-color: var(--body-color);
            line-height: 1.6;
            overflow-x: hidden;
        }
        
        h1, h2, h3, h4, h5, h6 {
            font-family: var(--font-heading);
            font-weight: 700;
            line-height: 1.2;
        }
        
        a {
            text-decoration: none;
            color: var(--primary-color);
            transition: all 0.3s ease;
        }
        
        a:hover {
            color: var(--secondary-color);
        }
        
        .text-gradient {
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-fill-color: transparent;
        }
        
        .btn {
            padding: 0.75rem 1.5rem;
            border-radius: var(--border-radius);
            font-weight: 500;
            transition: all 0.3s ease;
            font-size: 1rem;
        }
        
        .btn-primary {
            background: var(--primary-gradient);
            border: none;
            color: white;
            box-shadow: 0 4px 6px rgba(99, 102, 241, 0.25);
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #5254cc 0%, #7e50dd 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 8px rgba(99, 102, 241, 0.3);
        }
        
        .btn-outline {
            border: 1.5px solid var(--gray-light-color);
            color: var(--dark-color);
            background: transparent;
        }
        
        .btn-outline:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
        }
        
        .btn-outline.active {
            border-color: var(--primary-color);
            color: var(--primary-color);
        }
        
        .btn-sm {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
        }
        
        .btn-icon {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }
        
        .btn-icon-only {
            width: 2.5rem;
            height: 2.5rem;
            padding: 0;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }
        
        /* Dashboard Layout */
        .dashboard-container {
            display: flex;
            min-height: 100vh;
        }
        
        /* Sidebar */
        .sidebar {
            width: 280px;
            background-color: var(--white-color);
            border-right: 1px solid var(--gray-light-color);
            height: 100vh;
            position: fixed;
            top: 0;
            left: 0;
            z-index: 1000;
            transition: all 0.3s ease;
            overflow-y: auto;
        }
        
        .sidebar-header {
            padding: 1.5rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 1px solid var(--gray-light-color);
        }
        
        .logo-text {
            font-family: var(--font-heading);
            font-size: 1.5rem;
            font-weight: 700;
        }
        
        .sidebar-toggle {
            display: none;
            background: transparent;
            border: none;
            color: var(--gray-color);
            font-size: 1.5rem;
            cursor: pointer;
        }
        
        .sidebar-menu {
            padding: 1.5rem 0;
        }
        
        .menu-label {
            font-size: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            color: var(--gray-color);
            padding: 0 1.5rem;
            margin-bottom: 0.75rem;
            margin-top: 1.5rem;
        }
        
        .menu-item {
            padding: 0.75rem 1.5rem;
            display: flex;
            align-items: center;
            color: var(--gray-color);
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }
        
        .menu-item:hover {
            background-color: rgba(99, 102, 241, 0.05);
            color: var(--primary-color);
        }
        
        .menu-item.active {
            background-color: rgba(99, 102, 241, 0.1);
            color: var(--primary-color);
            border-left-color: var(--primary-color);
        }
        
        .menu-item i {
            font-size: 1.25rem;
            margin-right: 0.75rem;
            width: 1.5rem;
            text-align: center;
        }
        
        .menu-item span {
            font-weight: 500;
        }
        
        .sidebar-footer {
            padding: 1.5rem;
            border-top: 1px solid var(--gray-light-color);
        }
        
        .user-profile {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: var(--border-radius-full);
            background-color: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 1rem;
        }
        
        .user-info {
            flex: 1;
        }
        
        .user-name {
            font-weight: 600;
            margin-bottom: 0.25rem;
            font-size: 0.875rem;
        }
        
        .user-email {
            font-size: 0.75rem;
            color: var(--gray-color);
        }
        
        .dropdown-toggle {
            background: transparent;
            border: none;
            color: var(--gray-color);
            cursor: pointer;
        }
        
        /* Main Content */
        .main-content {
            flex: 1;
            margin-left: 280px;
            padding: 2rem;
            transition: all 0.3s ease;
        }
        
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }
        
        .page-title {
            font-size: 1.75rem;
            margin-bottom: 0.5rem;
        }
        
        .page-subtitle {
            color: var(--gray-color);
            font-size: 1rem;
        }
        
        .header-actions {
            display: flex;
            gap: 0.75rem;
        }
        
        /* Stats Cards */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background-color: var(--white-color);
            border-radius: var(--border-radius-lg);
            padding: 1.5rem;
            box-shadow: var(--shadow);
        }
        
        .stat-icon {
            width: 48px;
            height: 48px;
            border-radius: var(--border-radius);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: white;
        }
        
        .stat-icon.revenue {
            background-color: rgba(99, 102, 241, 0.1);
            color: var(--primary-color);
        }
        
        .stat-icon.orders {
            background-color: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
        }
        
        .stat-icon.customers {
            background-color: rgba(245, 158, 11, 0.1);
            color: var(--warning-color);
        }
        
        .stat-icon.conversion {
            background-color: rgba(59, 130, 246, 0.1);
            color: var(--info-color);
        }
        
        .stat-value {
            font-size: 1.75rem;
            font-weight: 700;
            margin-bottom: 0.25rem;
            font-family: var(--font-heading);
        }
        
        .stat-label {
            color: var(--gray-color);
            font-size: 0.875rem;
        }
        
        .stat-change {
            display: flex;
            align-items: center;
            gap: 0.25rem;
            font-size: 0.875rem;
            margin-top: 0.5rem;
        }
        
        .stat-change.positive {
            color: var(--success-color);
        }
        
        .stat-change.negative {
            color: var(--danger-color);
        }
        
        /* Charts and Tables */
        .dashboard-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .card {
            background-color: var(--white-color);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow);
            border: none;
            overflow: hidden;
        }
        
        .card-header {
            padding: 1.25rem 1.5rem;
            border-bottom: 1px solid var(--gray-light-color);
            background-color: transparent;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .card-title {
            font-size: 1.125rem;
            margin-bottom: 0;
        }
        
        .card-body {
            padding: 1.5rem;
        }
        
        .chart-container {
            height: 300px;
        }
        
        /* Recent Transactions */
        .transaction-list {
            margin: 0;
            padding: 0;
            list-style: none;
        }
        
        .transaction-item {
            display: flex;
            align-items: center;
            padding: 1rem 0;
            border-bottom: 1px solid var(--gray-light-color);
        }
        
        .transaction-item:last-child {
            border-bottom: none;
        }
        
        .transaction-icon {
            width: 40px;
            height: 40px;
            border-radius: var(--border-radius);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
            margin-right: 1rem;
        }
        
        .transaction-icon.payment {
            background-color: rgba(99, 102, 241, 0.1);
            color: var(--primary-color);
        }
        
        .transaction-icon.subscription {
            background-color: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
        }
        
        .transaction-icon.refund {
            background-color: rgba(239, 68, 68, 0.1);
            color: var(--danger-color);
        }
        
        .transaction-info {
            flex: 1;
        }
        
        .transaction-title {
            font-weight: 600;
            margin-bottom: 0.25rem;
        }
        
        .transaction-time {
            font-size: 0.75rem;
            color: var(--gray-color);
        }
        
        .transaction-amount {
            font-weight: 600;
            font-family: var(--font-heading);
        }
        
        .transaction-amount.positive {
            color: var(--success-color);
        }
        
        .transaction-amount.negative {
            color: var(--danger-color);
        }
        
        /* Recent Customers */
        .customer-list {
            margin: 0;
            padding: 0;
            list-style: none;
        }
        
        .customer-item {
            display: flex;
            align-items: center;
            padding: 1rem 0;
            border-bottom: 1px solid var(--gray-light-color);
        }
        
        .customer-item:last-child {
            border-bottom: none;
        }
        
        .customer-avatar {
            width: 40px;
            height: 40px;
            border-radius: var(--border-radius-full);
            background-color: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            margin-right: 1rem;
        }
        
        .customer-info {
            flex: 1;
        }
        
        .customer-name {
            font-weight: 600;
            margin-bottom: 0.25rem;
        }
        
        .customer-email {
            font-size: 0.75rem;
            color: var(--gray-color);
        }
        
        .customer-spent {
            font-weight: 600;
            font-family: var(--font-heading);
        }
        
        /* Responsive */
        @media (max-width: 1199.98px) {
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
        }
        
        @media (max-width: 991.98px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0;
            }
            
            .sidebar-toggle {
                display: block;
            }
            
            .mobile-header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 1rem;
                background-color: var(--white-color);
                box-shadow: var(--shadow);
                position: sticky;
                top: 0;
                z-index: 999;
            }
        }
        
        @media (max-width: 767.98px) {
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .page-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 1rem;
            }
            
            .header-actions {
                width: 100%;
                justify-content: space-between;
            }
        }
        
        /* Mobile Header (visible only on small screens) */
        .mobile-header {
            display: none;
        }
        
        /* Overlay for sidebar on mobile */
        .sidebar-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 999;
            display: none;
        }
        
        .sidebar-overlay.show {
            display: block;
        }
    </style>
</head>
<body>
    <!-- Mobile Header (visible only on small screens) -->
    <div class="mobile-header">
        <button class="sidebar-toggle" id="sidebarToggle">
            <i class='bx bx-menu'></i>
        </button>
        <a href="index.html" class="navbar-brand">
            <span class="logo-text">Pay<span class="text-gradient">Soft</span></span>
        </a>
        <div class="dropdown">
            <button class="dropdown-toggle" type="button" id="mobileUserMenu" data-bs-toggle="dropdown" aria-expanded="false">
                <i class='bx bx-user-circle' style="font-size: 1.5rem;"></i>
            </button>
            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="mobileUserMenu">
                <li><a class="dropdown-item" href="profile.html">Mon profil</a></li>
                <li><a class="dropdown-item" href="settings.html">Paramètres</a></li>
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item" href="index.html">Déconnexion</a></li>
            </ul>
        </div>
    </div>

    <!-- Sidebar Overlay -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>

    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <a href="index.html" class="navbar-brand">
                    <span class="logo-text">Pay<span class="text-gradient">Soft</span></span>
                </a>
                <button class="sidebar-toggle d-lg-none" id="closeSidebar">
                    <i class='bx bx-x'></i>
                </button>
            </div>
            
            <div class="sidebar-menu">
                <div class="menu-item active">
                    <i class='bx bxs-dashboard'></i>
                    <span>Tableau de bord</span>
                </div>
                
                <div class="menu-label">Paiements</div>
                
                <div class="menu-item">
                    <i class='bx bx-link'></i>
                    <span>Liens de paiement</span>
                </div>
                
                <div class="menu-item">
                    <i class='bx bx-receipt'></i>
                    <span>Factures</span>
                </div>
                
                <div class="menu-item">
                    <i class='bx bx-refresh'></i>
                    <span>Abonnements</span>
                </div>
                
                <div class="menu-item">
                    <i class='bx bx-store'></i>
                    <span>Point de vente</span>
                </div>
                
                <div class="menu-label">Gestion</div>
                
                <div class="menu-item">
                    <i class='bx bx-user'></i>
                    <span>Clients</span>
                </div>
                
                <div class="menu-item">
                    <i class='bx bx-transfer'></i>
                    <span>Transactions</span>
                </div>
                
                <div class="menu-item">
                    <i class='bx bx-wallet'></i>
                    <span>Portefeuille</span>
                </div>
                
                <div class="menu-label">Compte</div>
                
                <div class="menu-item">
                    <i class='bx bx-cog'></i>
                    <span>Paramètres</span>
                </div>
                
                <div class="menu-item">
                    <i class='bx bx-help-circle'></i>
                    <span>Aide & Support</span>
                </div>
            </div>
            
            <div class="sidebar-footer">
                <div class="user-profile">
                    <div class="user-avatar">AM</div>
                    <div class="user-info">
                        <div class="user-name">Amadou Mbaye</div>
                        <div class="user-email"><EMAIL></div>
                    </div>
                    <div class="dropdown">
                        <button class="dropdown-toggle" type="button" id="userMenu" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class='bx bx-dots-vertical-rounded'></i>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userMenu">
                            <li><a class="dropdown-item" href="profile.html">Mon profil</a></li>
                            <li><a class="dropdown-item" href="settings.html">Paramètres</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="index.html">Déconnexion</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <div class="page-header">
                <div>
                    <h1 class="page-title">Tableau de bord</h1>
                    <p class="page-subtitle">Bienvenue, Amadou! Voici un aperçu de votre activité.</p>
                </div>
                
                <div class="header-actions">
                    <button class="btn btn-outline btn-icon">
                        <i class='bx bx-calendar'></i>
                        <span>Derniers 30 jours</span>
                    </button>
                    <button class="btn btn-primary btn-icon">
                        <i class='bx bx-plus'></i>
                        <span>Nouveau paiement</span>
                    </button>
                </div>
            </div>
            
            <!-- Stats Cards -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon revenue">
                        <i class='bx bx-money'></i>
                    </div>
                    <div class="stat-value">2,450,000 FCFA</div>
                    <div class="stat-label">Revenus totaux</div>
                    <div class="stat-change positive">
                        <i class='bx bx-up-arrow-alt'></i>
                        <span>12.5% depuis le mois dernier</span>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon orders">
                        <i class='bx bx-cart'></i>
                    </div>
                    <div class="stat-value">156</div>
                    <div class="stat-label">Transactions</div>
                    <div class="stat-change positive">
                        <i class='bx bx-up-arrow-alt'></i>
                        <span>8.2% depuis le mois dernier</span>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon customers">
                        <i class='bx bx-user-plus'></i>
                    </div>
                    <div class="stat-value">42</div>
                    <div class="stat-label">Nouveaux clients</div>
                    <div class="stat-change positive">
                        <i class='bx bx-up-arrow-alt'></i>
                        <span>4.6% depuis le mois dernier</span>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon conversion">
                        <i class='bx bx-line-chart'></i>
                    </div>
                    <div class="stat-value">68.7%</div>
                    <div class="stat-label">Taux de conversion</div>
                    <div class="stat-change negative">
                        <i class='bx bx-down-arrow-alt'></i>
                        <span>1.8% depuis le mois dernier</span>
                    </div>
                </div>
            </div>
            
            <!-- Charts and Tables -->
            <div class="dashboard-grid">
                <!-- Revenue Chart -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">Revenus</h5>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-sm btn-outline active">Jour</button>
                            <button type="button" class="btn btn-sm btn-outline">Semaine</button>
                            <button type="button" class="btn btn-sm btn-outline">Mois</button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="chart-container" id="revenueChart"></div>
                    </div>
                </div>
                
                <!-- Recent Transactions -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">Transactions récentes</h5>
                        <a href="#" class="btn btn-sm btn-outline">Voir tout</a>
                    </div>
                    <div class="card-body">
                        <ul class="transaction-list">
                            <li class="transaction-item">
                                <div class="transaction-icon payment">
                                    <i class='bx bx-check'></i>
                                </div>
                                <div class="transaction-info">
                                    <div class="transaction-title">Paiement reçu</div>
                                    <div class="transaction-time">Il y a 2 heures</div>
                                </div>
                                <div class="transaction-amount positive">+75,000 FCFA</div>
                            </li>
                            
                            <li class="transaction-item">
                                <div class="transaction-icon subscription">
                                    <i class='bx bx-refresh'></i>
                                </div>
                                <div class="transaction-info">
                                    <div class="transaction-title">Abonnement mensuel</div>
                                    <div class="transaction-time">Il y a 1 jour</div>
                                </div>
                                <div class="transaction-amount positive">+25,000 FCFA</div>
                            </li>
                            
                            <li class="transaction-item">
                                <div class="transaction-icon refund">
                                    <i class='bx bx-undo'></i>
                                </div>
                                <div class="transaction-info">
                                    <div class="transaction-title">Remboursement</div>
                                    <div class="transaction-time">Il y a 2 jours</div>
                                </div>
                                <div class="transaction-amount negative">-15,000 FCFA</div>
                            </li>
                            
                            <li class="transaction-item">
                                <div class="transaction-icon payment">
                                    <i class='bx bx-check'></i>
                                </div>
                                <div class="transaction-info">
                                    <div class="transaction-title">Paiement reçu</div>
                                    <div class="transaction-time">Il y a 3 jours</div>
                                </div>
                                <div class="transaction-amount positive">+120,000 FCFA</div>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- Second Row -->
            <div class="dashboard-grid">
                <!-- Payment Methods Chart -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">Méthodes de paiement</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container" id="paymentMethodsChart"></div>
                    </div>
                </div>
                
                <!-- Recent Customers -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">Clients récents</h5>
                        <a href="#" class="btn btn-sm btn-outline">Voir tout</a>
                    </div>
                    <div class="card-body">
                        <ul class="customer-list">
                            <li class="customer-item">
                                <div class="customer-avatar">FB</div>
                                <div class="customer-info">
                                    <div class="customer-name">Fatou Bah</div>
                                    <div class="customer-email"><EMAIL></div>
                                </div>
                                <div class="customer-spent">75,000 FCFA</div>
                            </li>
                            
                            <li class="customer-item">
                                <div class="customer-avatar">MS</div>
                                <div class="customer-info">
                                    <div class="customer-name">Moussa Sow</div>
                                    <div class="customer-email"><EMAIL></div>
                                </div>
                                <div class="customer-spent">120,000 FCFA</div>
                            </li>
                            
                            <li class="customer-item">
                                <div class="customer-avatar">AD</div>
                                <div class="customer-info">
                                    <div class="customer-name">Aïcha Diallo</div>
                                    <div class="customer-email"><EMAIL></div>
                                </div>
                                <div class="customer-spent">25,000 FCFA</div>
                            </li>
                            
                            <li class="customer-item">
                                <div class="customer-avatar">OK</div>
                                <div class="customer-info">
                                    <div class="customer-name">Omar Keita</div>
                                    <div class="customer-email"><EMAIL></div>
                                </div>
                                <div class="customer-spent">50,000 FCFA</div>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/apexcharts@3.35.0/dist/apexcharts.min.js"></script>
    <script>
        // Sidebar Toggle
        const sidebarToggle = document.getElementById('sidebarToggle');
        const closeSidebar = document.getElementById('closeSidebar');
        const sidebar = document.getElementById('sidebar');
        const sidebarOverlay = document.getElementById('sidebarOverlay');
        
        if (sidebarToggle && sidebar) {
            sidebarToggle.addEventListener('click', function() {
                sidebar.classList.add('show');
                sidebarOverlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            });
        }
        
        if (closeSidebar && sidebar) {
            closeSidebar.addEventListener('click', function() {
                sidebar.classList.remove('show');
                sidebarOverlay.classList.remove('show');
                document.body.style.overflow = '';
            });
        }
        
        if (sidebarOverlay) {
            sidebarOverlay.addEventListener('click', function() {
                sidebar.classList.remove('show');
                sidebarOverlay.classList.remove('show');
                document.body.style.overflow = '';
            });
        }
        
        // Revenue Chart
        if (document.getElementById('revenueChart')) {
            const revenueChartOptions = {
                series: [{
                    name: 'Revenus',
                    data: [350000, 420000, 280000, 490000, 520000, 580000, 450000, 600000, 650000, 700000, 750000, 820000]
                }],
                chart: {
                    type: 'area',
                    height: 300,
                    toolbar: {
                        show: false
                    },
                    zoom: {
                        enabled: false
                    }
                },
                dataLabels: {
                    enabled: false
                },
                stroke: {
                    curve: 'smooth',
                    width: 3
                },
                colors: ['#6366f1'],
                fill: {
                    type: 'gradient',
                    gradient: {
                        shadeIntensity: 1,
                        opacityFrom: 0.7,
                        opacityTo: 0.2,
                        stops: [0, 90, 100]
                    }
                },
                xaxis: {
                    categories: ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Juin', 'Juil', 'Août', 'Sep', 'Oct', 'Nov', 'Déc'],
                    labels: {
                        style: {
                            colors: '#64748b'
                        }
                    },
                    axisBorder: {
                        show: false
                    }
                },
                yaxis: {
                    labels: {
                        formatter: function(val) {
                            return val.toLocaleString() + ' FCFA';
                        },
                        style: {
                            colors: '#64748b'
                        }
                    }
                },
                grid: {
                    borderColor: '#e2e8f0',
                    strokeDashArray: 4,
                    xaxis: {
                        lines: {
                            show: true
                        }
                    },
                    yaxis: {
                        lines: {
                            show: true
                        }
                    },
                    padding: {
                        top: 0,
                        right: 0,
                        bottom: 0,
                        left: 10
                    }
                },
                tooltip: {
                    y: {
                        formatter: function(val) {
                            return val.toLocaleString() + ' FCFA';
                        }
                    }
                }
            };
            
            const revenueChart = new ApexCharts(document.getElementById('revenueChart'), revenueChartOptions);
            revenueChart.render();
        }
        
        // Payment Methods Chart
        if (document.getElementById('paymentMethodsChart')) {
            const paymentMethodsChartOptions = {
                series: [45, 30, 15, 10],
                chart: {
                    type: 'donut',
                    height: 300
                },
                labels: ['Mobile Money', 'Cartes bancaires', 'Virements', 'Autres'],
                colors: ['#6366f1', '#10b981', '#f59e0b', '#3b82f6'],
                legend: {
                    position: 'bottom',
                    horizontalAlign: 'center',
                    fontSize: '14px',
                    markers: {
                        width: 12,
                        height: 12,
                        radius: 12
                    },
                    itemMargin: {
                        horizontal: 10,
                        vertical: 8
                    }
                },
                plotOptions: {
                    pie: {
                        donut: {
                            size: '65%',
                            labels: {
                                show: true,
                                name: {
                                    show: true,
                                    fontSize: '22px',
                                    fontFamily: 'var(--font-heading)',
                                    fontWeight: 600,
                                    color: undefined,
                                    offsetY: -10
                                },
                                value: {
                                    show: true,
                                    fontSize: '16px',
                                    fontFamily: 'var(--font-sans)',
                                    fontWeight: 400,
                                    color: undefined,
                                    offsetY: 16,
                                    formatter: function (val) {
                                        return val + '%';
                                    }
                                },
                                total: {
                                    show: true,
                                    showAlways: false,
                                    label: 'Total',
                                    fontSize: '16px',
                                    fontFamily: 'var(--font-heading)',
                                    fontWeight: 600,
                                    color: '#373d3f',
                                    formatter: function (w) {
                                        return w.globals.seriesTotals.reduce((a, b) => {
                                            return a + b;
                                        }, 0) + '%';
                                    }
                                }
                            }
                        }
                    }
                },
                dataLabels: {
                    enabled: false
                },
                responsive: [{
                    breakpoint: 480,
                    options: {
                        chart: {
                            height: 250
                        },
                        legend: {
                            position: 'bottom'
                        }
                    }
                }]
            };
            
            const paymentMethodsChart = new ApexCharts(document.getElementById('paymentMethodsChart'), paymentMethodsChartOptions);
            paymentMethodsChart.render();
        }
    </script>
</body>
</html>
// Main JavaScript File for PaySoft

document.addEventListener('DOMContentLoaded', function() {
    'use strict';

    // Initialize AOS (Animate On Scroll)
    AOS.init({
        duration: 800,
        easing: 'ease-in-out',
        once: true,
        mirror: false
    });

    // Sticky Header
    const header = document.querySelector('.navbar');
    window.addEventListener('scroll', function() {
        if (window.scrollY > 100) {
            header.classList.add('sticky');
        } else {
            header.classList.remove('sticky');
        }
    });

    // Mobile Navigation Toggle
    const navbarToggler = document.querySelector('.navbar-toggler');
    const navbarCollapse = document.querySelector('.navbar-collapse');

    if (navbarToggler) {
        navbarToggler.addEventListener('click', function() {
            navbarToggler.classList.toggle('active');
        });
    }

    // Close mobile menu when clicking on a nav item
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(function(navLink) {
        navLink.addEventListener('click', function() {
            if (window.innerWidth < 992) {
                navbarCollapse.classList.remove('show');
                navbarToggler.classList.remove('active');
            }
        });
    });

    // Smooth scroll for links with hashes
    document.querySelectorAll('a[href*="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            if (this.getAttribute('href').startsWith('#')) {
                e.preventDefault();
                const targetId = this.getAttribute('href');
                if (targetId !== '#') {
                    const targetElement = document.querySelector(targetId);
                    if (targetElement) {
                        window.scrollTo({
                            top: targetElement.offsetTop - 80,
                            behavior: 'smooth'
                        });
                    }
                }
            }
        });
    });

    // Back to top button
    const backToTop = document.querySelector('.back-to-top');
    if (backToTop) {
        window.addEventListener('scroll', function() {
            if (window.scrollY > 300) {
                backToTop.classList.add('active');
            } else {
                backToTop.classList.remove('active');
            }
        });

        backToTop.addEventListener('click', function(e) {
            e.preventDefault();
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }

    // Pricing toggle
    const pricingToggle = document.getElementById('pricing-toggle');
    if (pricingToggle) {
        const usagePrices = document.querySelectorAll('.usage-price');
        const proPrices = document.querySelectorAll('.pro-price');
        const toggleLabels = document.querySelectorAll('.pricing-toggle span');

        pricingToggle.addEventListener('change', function() {
            toggleLabels.forEach(label => label.classList.toggle('active'));
            
            if (this.checked) {
                usagePrices.forEach(price => price.style.display = 'none');
                proPrices.forEach(price => price.style.display = 'flex');
            } else {
                usagePrices.forEach(price => price.style.display = 'flex');
                proPrices.forEach(price => price.style.display = 'none');
            }
        });
    }

    // Video modal
    const playButton = document.getElementById('play-demo');
    if (playButton) {
        playButton.addEventListener('click', function() {
            // In a real implementation, this would open a video modal
            // For this demo, we'll just show an alert
            alert('Cette fonctionnalité ouvrirait une vidéo de démonstration dans une implémentation réelle.');
        });
    }

    // Testimonials slider (simplified version without a slider library)
    // In a real implementation, you would use a library like Swiper.js
    const testimonialItems = document.querySelectorAll('.testimonial-item');
    let currentTestimonial = 0;

    function showTestimonials() {
        // This is a simplified version that doesn't actually slide
        // In a real implementation, you would use a proper slider library
        testimonialItems.forEach((item, index) => {
            if (window.innerWidth >= 768) {
                item.style.display = 'block'; // On larger screens, show all testimonials
            } else {
                item.style.display = index === currentTestimonial ? 'block' : 'none';
            }
        });
    }

    // Initialize testimonials display
    if (testimonialItems.length > 0) {
        showTestimonials();
        
        // For mobile, add auto-rotation (in a real implementation, you would add navigation controls)
        if (window.innerWidth < 768) {
            setInterval(() => {
                currentTestimonial = (currentTestimonial + 1) % testimonialItems.length;
                showTestimonials();
            }, 5000);
        }

        // Update on window resize
        window.addEventListener('resize', showTestimonials);
    }

    // Form validation (for a real implementation)
    const forms = document.querySelectorAll('.needs-validation');
    if (forms.length > 0) {
        Array.from(forms).forEach(form => {
            form.addEventListener('submit', event => {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }
});
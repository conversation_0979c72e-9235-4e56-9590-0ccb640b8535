# PaySoft - Agent Development Guide

## Project Overview
PaySoft is a static frontend for a Pan-African billing platform that enables users to create payment links for invoices, subscriptions, and debt collection. The project is built with vanilla HTML, CSS, and JavaScript using CDN dependencies.

## Build/Development Commands
This is a static website with no build system. Development workflow:
- **Local Development**: Open HTML files directly in browser or use a local server
- **Live Server**: Never start a live server
- **Testing**: Ask the user to verify functionality manually.
- **Deployment**: Upload static files to web server

## Architecture & Structure
```
├── assets/
│   ├── css/style.css          # Main stylesheet with CSS variables
│   └── js/main.js             # Main JavaScript functionality
├── *.html                     # Individual page templates
└── Brief.md                   # Project requirements and specifications
```

### Key Pages
- `index.html` - Landing page with hero section and features
- `dashboard.html` - User dashboard with charts and analytics
- `login.html` / `signup.html` - Authentication pages
- `settings.html` - User settings and API configuration
- `payment-links.html` - Payment link management
- `transactions.html` - Transaction history
- `customers.html` - Customer management
- `create-payment.html` - Payment creation form

### Dependencies (CDN-based)
- Must be Tailwind CSS - UI framework
- Boxicons 2.1.4 - Icon library
- AOS 2.3.4 - Scroll animations
- ApexCharts 3.35.0 - Dashboard charts
- Google Fonts - Typography (Inter, Space Grotesk, Work Sans)

## Code Style Guidelines

### CSS
- Use CSS custom properties (variables) defined in `:root`
- Follow BEM-like naming for custom classes
- Maintain consistent color scheme via CSS variables
- Use Bootstrap classes for layout, custom CSS for branding

### JavaScript
- Use vanilla JavaScript with modern ES6+ features
- Strict mode enabled (`'use strict'`)
- Event delegation and proper DOM ready handling
- Modular functions with clear separation of concerns

### HTML
- Semantic HTML5 structure
- French language content (`lang="fr"`)
- Proper meta tags for SEO and mobile responsiveness
- Accessibility considerations with proper ARIA labels

### Naming Conventions
- CSS: kebab-case for classes, camelCase for JavaScript
- Files: kebab-case for HTML files
- Variables: CSS custom properties with `--` prefix
- Functions: camelCase with descriptive names

### Error Handling
- Defensive programming with null checks
- Graceful degradation for missing elements
- Console logging for debugging (remove in production)

## Development Notes
- No backend integration yet - forms and APIs are placeholder
- Mobile-first responsive design approach
- Focus on African payment methods (Mobile Money, etc.)
- Multi-currency support (XOF, GHS, KES, NGN, USD, EUR)
- White-label customization capabilities planned

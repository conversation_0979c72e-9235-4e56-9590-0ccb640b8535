<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Créer un lien de paiement - PaySoft</title>
    <meta name="description" content="Créez un lien de paiement personnalisé pour recevoir des paiements rapidement.">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/boxicons@2.1.4/css/boxicons.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&family=Work+Sans:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #6366f1;
            --secondary-color: #4f46e5;
            --accent-color: #8b5cf6;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --info-color: #3b82f6;
            --dark-color: #1e293b;
            --gray-color: #64748b;
            --gray-light-color: #e2e8f0;
            --body-color: #f8fafc;
            --white-color: #ffffff;
            
            --primary-gradient: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
            
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
            
            --border-radius: 0.375rem;
            --border-radius-md: 0.5rem;
            --border-radius-lg: 0.75rem;
            --border-radius-xl: 1rem;
            --border-radius-2xl: 1.5rem;
            --border-radius-full: 9999px;
            
            --font-sans: 'Work Sans', sans-serif;
            --font-heading: 'Space Grotesk', sans-serif;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: var(--font-sans);
            color: var(--dark-color);
            background-color: var(--body-color);
            line-height: 1.6;
            overflow-x: hidden;
        }
        
        h1, h2, h3, h4, h5, h6 {
            font-family: var(--font-heading);
            font-weight: 700;
            line-height: 1.2;
        }
        
        a {
            text-decoration: none;
            color: var(--primary-color);
            transition: all 0.3s ease;
        }
        
        a:hover {
            color: var(--secondary-color);
        }
        
        .text-gradient {
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-fill-color: transparent;
        }
        
        .btn {
            padding: 0.75rem 1.5rem;
            border-radius: var(--border-radius);
            font-weight: 500;
            transition: all 0.3s ease;
            font-size: 1rem;
        }
        
        .btn-primary {
            background: var(--primary-gradient);
            border: none;
            color: white;
            box-shadow: 0 4px 6px rgba(99, 102, 241, 0.25);
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #5254cc 0%, #7e50dd 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 8px rgba(99, 102, 241, 0.3);
        }
        
        .btn-outline {
            border: 1.5px solid var(--gray-light-color);
            color: var(--dark-color);
            background: transparent;
        }
        
        .btn-outline:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
        }
        
        .btn-outline.active {
            border-color: var(--primary-color);
            color: var(--primary-color);
        }
        
        .btn-sm {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
        }
        
        .btn-icon {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }
        
        .btn-icon-only {
            width: 2.5rem;
            height: 2.5rem;
            padding: 0;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }
        
        /* Dashboard Layout */
        .dashboard-container {
            display: flex;
            min-height: 100vh;
        }
        
        /* Sidebar */
        .sidebar {
            width: 280px;
            background-color: var(--white-color);
            border-right: 1px solid var(--gray-light-color);
            height: 100vh;
            position: fixed;
            top: 0;
            left: 0;
            z-index: 1000;
            transition: all 0.3s ease;
            overflow-y: auto;
        }
        
        .sidebar-header {
            padding: 1.5rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 1px solid var(--gray-light-color);
        }
        
        .logo-text {
            font-family: var(--font-heading);
            font-size: 1.5rem;
            font-weight: 700;
        }
        
        .sidebar-toggle {
            display: none;
            background: transparent;
            border: none;
            color: var(--gray-color);
            font-size: 1.5rem;
            cursor: pointer;
        }
        
        .sidebar-menu {
            padding: 1.5rem 0;
        }
        
        .menu-label {
            font-size: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            color: var(--gray-color);
            padding: 0 1.5rem;
            margin-bottom: 0.75rem;
            margin-top: 1.5rem;
        }
        
        .menu-item {
            padding: 0.75rem 1.5rem;
            display: flex;
            align-items: center;
            color: var(--gray-color);
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }
        
        .menu-item:hover {
            background-color: rgba(99, 102, 241, 0.05);
            color: var(--primary-color);
        }
        
        .menu-item.active {
            background-color: rgba(99, 102, 241, 0.1);
            color: var(--primary-color);
            border-left-color: var(--primary-color);
        }
        
        .menu-item i {
            font-size: 1.25rem;
            margin-right: 0.75rem;
            width: 1.5rem;
            text-align: center;
        }
        
        .menu-item span {
            font-weight: 500;
        }
        
        .sidebar-footer {
            padding: 1.5rem;
            border-top: 1px solid var(--gray-light-color);
        }
        
        .user-profile {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: var(--border-radius-full);
            background-color: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 1rem;
        }
        
        .user-info {
            flex: 1;
        }
        
        .user-name {
            font-weight: 600;
            margin-bottom: 0.25rem;
            font-size: 0.875rem;
        }
        
        .user-email {
            font-size: 0.75rem;
            color: var(--gray-color);
        }
        
        .dropdown-toggle {
            background: transparent;
            border: none;
            color: var(--gray-color);
            cursor: pointer;
        }
        
        /* Main Content */
        .main-content {
            flex: 1;
            margin-left: 280px;
            padding: 2rem;
            transition: all 0.3s ease;
        }
        
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }
        
        .page-title {
            font-size: 1.75rem;
            margin-bottom: 0.5rem;
        }
        
        .page-subtitle {
            color: var(--gray-color);
            font-size: 1rem;
        }
        
        /* Form Styles */
        .form-card {
            background-color: var(--white-color);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .form-section {
            margin-bottom: 2rem;
        }
        
        .form-section:last-child {
            margin-bottom: 0;
        }
        
        .form-section-title {
            font-size: 1.25rem;
            margin-bottom: 1.5rem;
            padding-bottom: 0.75rem;
            border-bottom: 1px solid var(--gray-light-color);
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-label {
            font-weight: 600;
            margin-bottom: 0.5rem;
            display: block;
        }
        
        .form-text {
            font-size: 0.875rem;
            color: var(--gray-color);
            margin-top: 0.25rem;
        }
        
        .form-control {
            padding: 0.75rem 1rem;
            border-radius: var(--border-radius);
            border: 1px solid var(--gray-light-color);
            font-size: 1rem;
            width: 100%;
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }
        
        .form-select {
            padding: 0.75rem 1rem;
            border-radius: var(--border-radius);
            border: 1px solid var(--gray-light-color);
            font-size: 1rem;
            width: 100%;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 1rem center;
            background-size: 16px 12px;
            appearance: none;
        }
        
        .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }
        
        .form-check {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
        }
        
        .form-check-input {
            width: 1.25rem;
            height: 1.25rem;
            margin-top: 0;
        }
        
        .form-check-label {
            font-size: 1rem;
            cursor: pointer;
        }
        
        .input-group {
            display: flex;
            align-items: center;
        }
        
        .input-group-text {
            padding: 0.75rem 1rem;
            background-color: var(--gray-light-color);
            border: 1px solid var(--gray-light-color);
            border-radius: var(--border-radius) 0 0 var(--border-radius);
            font-size: 1rem;
            color: var(--gray-color);
        }
        
        .input-group .form-control {
            border-radius: 0 var(--border-radius) var(--border-radius) 0;
            border-left: none;
        }
        
        .form-row {
            display: flex;
            gap: 1rem;
        }
        
        .form-row .form-group {
            flex: 1;
        }
        
        /* Payment Preview */
        .payment-preview {
            background-color: var(--white-color);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow);
            padding: 2rem;
            position: sticky;
            top: 2rem;
        }
        
        .preview-header {
            margin-bottom: 1.5rem;
            padding-bottom: 0.75rem;
            border-bottom: 1px solid var(--gray-light-color);
        }
        
        .preview-title {
            font-size: 1.25rem;
            margin-bottom: 0.5rem;
        }
        
        .preview-subtitle {
            color: var(--gray-color);
            font-size: 0.875rem;
        }
        
        .preview-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 1rem;
        }
        
        .preview-label {
            color: var(--gray-color);
            font-size: 0.875rem;
        }
        
        .preview-value {
            font-weight: 600;
        }
        
        .preview-total {
            display: flex;
            justify-content: space-between;
            margin-top: 1.5rem;
            padding-top: 1rem;
            border-top: 1px solid var(--gray-light-color);
            font-family: var(--font-heading);
        }
        
        .preview-total-label {
            font-size: 1.125rem;
            font-weight: 600;
        }
        
        .preview-total-value {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--primary-color);
        }
        
        .preview-link {
            margin-top: 1.5rem;
            padding: 1rem;
            background-color: rgba(99, 102, 241, 0.05);
            border-radius: var(--border-radius);
            border: 1px dashed var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .preview-link-text {
            font-size: 0.875rem;
            color: var(--gray-color);
            word-break: break-all;
        }
        
        .preview-link-copy {
            background: transparent;
            border: none;
            color: var(--primary-color);
            cursor: pointer;
            font-size: 1.25rem;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0.25rem;
        }
        
        /* Mobile Header (visible only on small screens) */
        .mobile-header {
            display: none;
        }
        
        /* Overlay for sidebar on mobile */
        .sidebar-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 999;
            display: none;
        }
        
        .sidebar-overlay.show {
            display: block;
        }
        
        /* Responsive */
        @media (max-width: 991.98px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0;
            }
            
            .sidebar-toggle {
                display: block;
            }
            
            .mobile-header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 1rem;
                background-color: var(--white-color);
                box-shadow: var(--shadow);
                position: sticky;
                top: 0;
                z-index: 999;
            }
            
            .form-row {
                flex-direction: column;
                gap: 1.5rem;
            }
        }
        
        @media (max-width: 767.98px) {
            .page-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Mobile Header (visible only on small screens) -->
    <div class="mobile-header">
        <button class="sidebar-toggle" id="sidebarToggle">
            <i class='bx bx-menu'></i>
        </button>
        <a href="index.html" class="navbar-brand">
            <span class="logo-text">Pay<span class="text-gradient">Soft</span></span>
        </a>
        <div class="dropdown">
            <button class="dropdown-toggle" type="button" id="mobileUserMenu" data-bs-toggle="dropdown" aria-expanded="false">
                <i class='bx bx-user-circle' style="font-size: 1.5rem;"></i>
            </button>
            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="mobileUserMenu">
                <li><a class="dropdown-item" href="profile.html">Mon profil</a></li>
                <li><a class="dropdown-item" href="settings.html">Paramètres</a></li>
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item" href="index.html">Déconnexion</a></li>
            </ul>
        </div>
    </div>

    <!-- Sidebar Overlay -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>

    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <a href="index.html" class="navbar-brand">
                    <span class="logo-text">Pay<span class="text-gradient">Soft</span></span>
                </a>
                <button class="sidebar-toggle d-lg-none" id="closeSidebar">
                    <i class='bx bx-x'></i>
                </button>
            </div>
            
            <div class="sidebar-menu">
                <div class="menu-item">
                    <i class='bx bxs-dashboard'></i>
                    <span>Tableau de bord</span>
                </div>
                
                <div class="menu-label">Paiements</div>
                
                <div class="menu-item active">
                    <i class='bx bx-link'></i>
                    <span>Liens de paiement</span>
                </div>
                
                <div class="menu-item">
                    <i class='bx bx-receipt'></i>
                    <span>Factures</span>
                </div>
                
                <div class="menu-item">
                    <i class='bx bx-refresh'></i>
                    <span>Abonnements</span>
                </div>
                
                <div class="menu-item">
                    <i class='bx bx-store'></i>
                    <span>Point de vente</span>
                </div>
                
                <div class="menu-label">Gestion</div>
                
                <div class="menu-item">
                    <i class='bx bx-user'></i>
                    <span>Clients</span>
                </div>
                
                <div class="menu-item">
                    <i class='bx bx-transfer'></i>
                    <span>Transactions</span>
                </div>
                
                <div class="menu-item">
                    <i class='bx bx-wallet'></i>
                    <span>Portefeuille</span>
                </div>
                
                <div class="menu-label">Compte</div>
                
                <div class="menu-item">
                    <i class='bx bx-cog'></i>
                    <span>Paramètres</span>
                </div>
                
                <div class="menu-item">
                    <i class='bx bx-help-circle'></i>
                    <span>Aide & Support</span>
                </div>
            </div>
            
            <div class="sidebar-footer">
                <div class="user-profile">
                    <div class="user-avatar">AM</div>
                    <div class="user-info">
                        <div class="user-name">Amadou Mbaye</div>
                        <div class="user-email"><EMAIL></div>
                    </div>
                    <div class="dropdown">
                        <button class="dropdown-toggle" type="button" id="userMenu" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class='bx bx-dots-vertical-rounded'></i>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userMenu">
                            <li><a class="dropdown-item" href="profile.html">Mon profil</a></li>
                            <li><a class="dropdown-item" href="settings.html">Paramètres</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="index.html">Déconnexion</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <div class="page-header">
                <div>
                    <h1 class="page-title">Créer un lien de paiement</h1>
                    <p class="page-subtitle">Créez un lien de paiement personnalisé pour recevoir des paiements rapidement</p>
                </div>
                
                <div class="header-actions">
                    <a href="dashboard.html" class="btn btn-outline btn-icon">
                        <i class='bx bx-arrow-back'></i>
                        <span>Retour</span>
                    </a>
                </div>
            </div>
            
            <div class="row">
                <div class="col-lg-8">
                    <!-- Payment Form -->
                    <form id="paymentForm">
                        <!-- Basic Information -->
                        <div class="form-card">
                            <div class="form-section">
                                <h3 class="form-section-title">Informations de base</h3>
                                
                                <div class="form-group">
                                    <label for="paymentTitle" class="form-label">Titre du paiement</label>
                                    <input type="text" class="form-control" id="paymentTitle" placeholder="ex: Consultation, Abonnement mensuel, etc." required>
                                    <div class="form-text">Ce titre sera visible par vos clients sur la page de paiement.</div>
                                </div>
                                
                                <div class="form-group">
                                    <label for="paymentDescription" class="form-label">Description (optionnel)</label>
                                    <textarea class="form-control" id="paymentDescription" rows="3" placeholder="Décrivez brièvement ce que vous vendez"></textarea>
                                </div>
                                
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="paymentAmount" class="form-label">Montant</label>
                                        <div class="input-group">
                                            <input type="number" class="form-control" id="paymentAmount" placeholder="0.00" min="0" step="0.01" required>
                                            <select class="form-select" id="paymentCurrency" style="max-width: 120px;">
                                                <option value="XOF">FCFA</option>
                                                <option value="USD">USD</option>
                                                <option value="EUR">EUR</option>
                                                <option value="GHS">GHS</option>
                                                <option value="NGN">NGN</option>
                                                <option value="KES">KES</option>
                                            </select>
                                        </div>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="paymentType" class="form-label">Type de paiement</label>
                                        <select class="form-select" id="paymentType">
                                            <option value="one-time">Paiement unique</option>
                                            <option value="subscription">Abonnement récurrent</option>
                                            <option value="donation">Don (montant libre)</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <div id="subscriptionOptions" style="display: none;">
                                    <div class="form-row">
                                        <div class="form-group">
                                            <label for="subscriptionInterval" class="form-label">Intervalle</label>
                                            <select class="form-select" id="subscriptionInterval">
                                                <option value="weekly">Hebdomadaire</option>
                                                <option value="monthly" selected>Mensuel</option>
                                                <option value="quarterly">Trimestriel</option>
                                                <option value="yearly">Annuel</option>
                                            </select>
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="subscriptionDuration" class="form-label">Durée</label>
                                            <select class="form-select" id="subscriptionDuration">
                                                <option value="unlimited">Illimitée (jusqu'à annulation)</option>
                                                <option value="3">3 mois</option>
                                                <option value="6">6 mois</option>
                                                <option value="12">12 mois</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Customer Information -->
                        <div class="form-card">
                            <div class="form-section">
                                <h3 class="form-section-title">Informations client</h3>
                                
                                <div class="form-group">
                                    <label class="form-label">Champs à collecter</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="collectName" checked>
                                        <label class="form-check-label" for="collectName">Nom complet</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="collectEmail" checked>
                                        <label class="form-check-label" for="collectEmail">Email</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="collectPhone" checked>
                                        <label class="form-check-label" for="collectPhone">Numéro de téléphone</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="collectAddress">
                                        <label class="form-check-label" for="collectAddress">Adresse</label>
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label for="customField" class="form-label">Champ personnalisé (optionnel)</label>
                                    <input type="text" class="form-control" id="customField" placeholder="ex: Numéro de commande, Référence, etc.">
                                </div>
                            </div>
                        </div>
                        
                        <!-- Payment Methods -->
                        <div class="form-card">
                            <div class="form-section">
                                <h3 class="form-section-title">Méthodes de paiement</h3>
                                
                                <div class="form-group">
                                    <label class="form-label">Méthodes acceptées</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="acceptMobileMoney" checked>
                                        <label class="form-check-label" for="acceptMobileMoney">Mobile Money (Orange Money, MTN Mobile Money, etc.)</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="acceptCards" checked>
                                        <label class="form-check-label" for="acceptCards">Cartes bancaires (Visa, Mastercard)</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="acceptBankTransfer">
                                        <label class="form-check-label" for="acceptBankTransfer">Virement bancaire</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Customization -->
                        <div class="form-card">
                            <div class="form-section">
                                <h3 class="form-section-title">Personnalisation</h3>
                                
                                <div class="form-group">
                                    <label for="successMessage" class="form-label">Message de succès</label>
                                    <textarea class="form-control" id="successMessage" rows="2">Merci pour votre paiement! Nous avons bien reçu votre transaction.</textarea>
                                </div>
                                
                                <div class="form-group">
                                    <label for="redirectUrl" class="form-label">URL de redirection (optionnel)</label>
                                    <input type="url" class="form-control" id="redirectUrl" placeholder="https://votre-site.com/merci">
                                    <div class="form-text">Après le paiement, le client sera redirigé vers cette URL.</div>
                                </div>
                                
                                <div class="form-group mb-0">
                                    <label class="form-label">Options avancées</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="sendReceipt" checked>
                                        <label class="form-check-label" for="sendReceipt">Envoyer un reçu par email</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="enableNotifications" checked>
                                        <label class="form-check-label" for="enableNotifications">Recevoir des notifications pour chaque paiement</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="limitPayments">
                                        <label class="form-check-label" for="limitPayments">Limiter le nombre de paiements</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end mb-4">
                            <button type="button" class="btn btn-outline">Enregistrer comme brouillon</button>
                            <button type="submit" class="btn btn-primary">Créer le lien de paiement</button>
                        </div>
                    </form>
                </div>
                
                <div class="col-lg-4">
                    <!-- Payment Preview -->
                    <div class="payment-preview">
                        <div class="preview-header">
                            <h4 class="preview-title">Aperçu</h4>
                            <p class="preview-subtitle">Voici à quoi ressemblera votre lien de paiement</p>
                        </div>
                        
                        <div class="preview-item">
                            <div class="preview-label">Titre</div>
                            <div class="preview-value" id="previewTitle">Consultation</div>
                        </div>
                        
                        <div class="preview-item">
                            <div class="preview-label">Description</div>
                            <div class="preview-value" id="previewDescription">Consultation professionnelle de 1 heure</div>
                        </div>
                        
                        <div class="preview-item">
                            <div class="preview-label">Type</div>
                            <div class="preview-value" id="previewType">Paiement unique</div>
                        </div>
                        
                        <div class="preview-item">
                            <div class="preview-label">Méthodes de paiement</div>
                            <div class="preview-value" id="previewMethods">Mobile Money, Cartes</div>
                        </div>
                        
                        <div class="preview-total">
                            <div class="preview-total-label">Montant</div>
                            <div class="preview-total-value" id="previewAmount">25,000 FCFA</div>
                        </div>
                        
                        <div class="preview-link">
                            <div class="preview-link-text" id="previewLink">paysoft.com/p/consultation-25000</div>
                            <button class="preview-link-copy" id="copyLink" title="Copier le lien">
                                <i class='bx bx-copy'></i>
                            </button>
                        </div>
                        
                        <div class="d-grid gap-2 mt-4">
                            <button type="button" class="btn btn-outline btn-icon">
                                <i class='bx bx-show'></i>
                                <span>Prévisualiser</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Sidebar Toggle
        const sidebarToggle = document.getElementById('sidebarToggle');
        const closeSidebar = document.getElementById('closeSidebar');
        const sidebar = document.getElementById('sidebar');
        const sidebarOverlay = document.getElementById('sidebarOverlay');
        
        if (sidebarToggle && sidebar) {
            sidebarToggle.addEventListener('click', function() {
                sidebar.classList.add('show');
                sidebarOverlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            });
        }
        
        if (closeSidebar && sidebar) {
            closeSidebar.addEventListener('click', function() {
                sidebar.classList.remove('show');
                sidebarOverlay.classList.remove('show');
                document.body.style.overflow = '';
            });
        }
        
        if (sidebarOverlay) {
            sidebarOverlay.addEventListener('click', function() {
                sidebar.classList.remove('show');
                sidebarOverlay.classList.remove('show');
                document.body.style.overflow = '';
            });
        }
        
        // Payment Type Toggle
        const paymentType = document.getElementById('paymentType');
        const subscriptionOptions = document.getElementById('subscriptionOptions');
        
        if (paymentType && subscriptionOptions) {
            paymentType.addEventListener('change', function() {
                if (this.value === 'subscription') {
                    subscriptionOptions.style.display = 'block';
                } else {
                    subscriptionOptions.style.display = 'none';
                }
                updatePreview();
            });
        }
        
        // Live Preview Update
        const paymentTitle = document.getElementById('paymentTitle');
        const paymentDescription = document.getElementById('paymentDescription');
        const paymentAmount = document.getElementById('paymentAmount');
        const paymentCurrency = document.getElementById('paymentCurrency');
        const acceptMobileMoney = document.getElementById('acceptMobileMoney');
        const acceptCards = document.getElementById('acceptCards');
        const acceptBankTransfer = document.getElementById('acceptBankTransfer');
        
        const previewTitle = document.getElementById('previewTitle');
        const previewDescription = document.getElementById('previewDescription');
        const previewType = document.getElementById('previewType');
        const previewMethods = document.getElementById('previewMethods');
        const previewAmount = document.getElementById('previewAmount');
        const previewLink = document.getElementById('previewLink');
        
        function updatePreview() {
            // Update title
            if (paymentTitle.value) {
                previewTitle.textContent = paymentTitle.value;
            } else {
                previewTitle.textContent = 'Consultation';
            }
            
            // Update description
            if (paymentDescription.value) {
                previewDescription.textContent = paymentDescription.value;
            } else {
                previewDescription.textContent = 'Consultation professionnelle de 1 heure';
            }
            
            // Update type
            if (paymentType.value === 'one-time') {
                previewType.textContent = 'Paiement unique';
            } else if (paymentType.value === 'subscription') {
                const interval = document.getElementById('subscriptionInterval').value;
                if (interval === 'monthly') {
                    previewType.textContent = 'Abonnement mensuel';
                } else if (interval === 'yearly') {
                    previewType.textContent = 'Abonnement annuel';
                } else if (interval === 'weekly') {
                    previewType.textContent = 'Abonnement hebdomadaire';
                } else {
                    previewType.textContent = 'Abonnement trimestriel';
                }
            } else {
                previewType.textContent = 'Don (montant libre)';
            }
            
            // Update payment methods
            const methods = [];
            if (acceptMobileMoney.checked) methods.push('Mobile Money');
            if (acceptCards.checked) methods.push('Cartes');
            if (acceptBankTransfer.checked) methods.push('Virement');
            
            previewMethods.textContent = methods.join(', ') || 'Aucune méthode sélectionnée';
            
            // Update amount
            let amount = paymentAmount.value ? parseFloat(paymentAmount.value) : 25000;
            let currency = paymentCurrency.value || 'XOF';
            let currencySymbol = 'FCFA';
            
            if (currency === 'USD') currencySymbol = '$';
            else if (currency === 'EUR') currencySymbol = '€';
            else if (currency === 'GHS') currencySymbol = 'GH₵';
            else if (currency === 'NGN') currencySymbol = '₦';
            else if (currency === 'KES') currencySymbol = 'KSh';
            
            if (currency === 'XOF') {
                previewAmount.textContent = amount.toLocaleString() + ' ' + currencySymbol;
            } else {
                previewAmount.textContent = currencySymbol + amount.toLocaleString();
            }
            
            // Update link
            const slug = paymentTitle.value ? paymentTitle.value.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '') : 'consultation';
            previewLink.textContent = 'paysoft.com/p/' + slug + '-' + (amount || '25000');
        }
        
        // Add event listeners to form fields
        const formFields = [
            paymentTitle, paymentDescription, paymentAmount, paymentCurrency, paymentType,
            acceptMobileMoney, acceptCards, acceptBankTransfer
        ];
        
        formFields.forEach(field => {
            if (field) {
                field.addEventListener('input', updatePreview);
                field.addEventListener('change', updatePreview);
            }
        });
        
        // Initialize preview
        document.addEventListener('DOMContentLoaded', function() {
            updatePreview();
            
            // Set initial values for the form
            paymentTitle.value = 'Consultation';
            paymentDescription.value = 'Consultation professionnelle de 1 heure';
            paymentAmount.value = '25000';
        });
        
        // Copy link functionality
        const copyLink = document.getElementById('copyLink');
        if (copyLink) {
            copyLink.addEventListener('click', function() {
                const linkText = previewLink.textContent;
                navigator.clipboard.writeText(linkText).then(() => {
                    // Show success message
                    const originalIcon = copyLink.innerHTML;
                    copyLink.innerHTML = '<i class="bx bx-check"></i>';
                    setTimeout(() => {
                        copyLink.innerHTML = originalIcon;
                    }, 2000);
                });
            });
        }
        
        // Form submission
        const paymentForm = document.getElementById('paymentForm');
        if (paymentForm) {
            paymentForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                // Show success message (in a real app, this would submit to the server)
                alert('Lien de paiement créé avec succès! Vous pouvez maintenant le partager avec vos clients.');
                
                // In a real app, redirect to the payment links list
                // window.location.href = 'payment-links.html';
            });
        }
    </script>
</body>
</html>
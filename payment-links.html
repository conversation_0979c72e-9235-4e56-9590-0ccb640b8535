<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Liens de paiement - PaySoft</title>
    <meta name="description" content="Gérez tous vos liens de paiement et suivez leurs performances.">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/boxicons@2.1.4/css/boxicons.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&family=Work+Sans:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #6366f1;
            --secondary-color: #4f46e5;
            --accent-color: #8b5cf6;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --info-color: #3b82f6;
            --dark-color: #1e293b;
            --gray-color: #64748b;
            --gray-light-color: #e2e8f0;
            --body-color: #f8fafc;
            --white-color: #ffffff;
            
            --primary-gradient: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
            
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
            
            --border-radius: 0.375rem;
            --border-radius-md: 0.5rem;
            --border-radius-lg: 0.75rem;
            --border-radius-xl: 1rem;
            --border-radius-2xl: 1.5rem;
            --border-radius-full: 9999px;
            
            --font-sans: 'Work Sans', sans-serif;
            --font-heading: 'Space Grotesk', sans-serif;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: var(--font-sans);
            color: var(--dark-color);
            background-color: var(--body-color);
            line-height: 1.6;
            overflow-x: hidden;
        }
        
        h1, h2, h3, h4, h5, h6 {
            font-family: var(--font-heading);
            font-weight: 700;
            line-height: 1.2;
        }
        
        a {
            text-decoration: none;
            color: var(--primary-color);
            transition: all 0.3s ease;
        }
        
        a:hover {
            color: var(--secondary-color);
        }
        
        .text-gradient {
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-fill-color: transparent;
        }
        
        .btn {
            padding: 0.75rem 1.5rem;
            border-radius: var(--border-radius);
            font-weight: 500;
            transition: all 0.3s ease;
            font-size: 1rem;
        }
        
        .btn-primary {
            background: var(--primary-gradient);
            border: none;
            color: white;
            box-shadow: 0 4px 6px rgba(99, 102, 241, 0.25);
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #5254cc 0%, #7e50dd 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 8px rgba(99, 102, 241, 0.3);
        }
        
        .btn-outline {
            border: 1.5px solid var(--gray-light-color);
            color: var(--dark-color);
            background: transparent;
        }
        
        .btn-outline:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
        }
        
        .btn-sm {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
        }
        
        .btn-icon {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }
        
        .btn-icon-only {
            width: 2.5rem;
            height: 2.5rem;
            padding: 0;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }
        
        /* Dashboard Layout */
        .dashboard-container {
            display: flex;
            min-height: 100vh;
        }
        
        /* Sidebar */
        .sidebar {
            width: 280px;
            background-color: var(--white-color);
            border-right: 1px solid var(--gray-light-color);
            height: 100vh;
            position: fixed;
            top: 0;
            left: 0;
            z-index: 1000;
            transition: all 0.3s ease;
            overflow-y: auto;
        }
        
        .sidebar-header {
            padding: 1.5rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 1px solid var(--gray-light-color);
        }
        
        .logo-text {
            font-family: var(--font-heading);
            font-size: 1.5rem;
            font-weight: 700;
        }
        
        .sidebar-toggle {
            display: none;
            background: transparent;
            border: none;
            color: var(--gray-color);
            font-size: 1.5rem;
            cursor: pointer;
        }
        
        .sidebar-menu {
            padding: 1.5rem 0;
        }
        
        .menu-label {
            font-size: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            color: var(--gray-color);
            padding: 0 1.5rem;
            margin-bottom: 0.75rem;
            margin-top: 1.5rem;
        }
        
        .menu-item {
            padding: 0.75rem 1.5rem;
            display: flex;
            align-items: center;
            color: var(--gray-color);
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }
        
        .menu-item:hover {
            background-color: rgba(99, 102, 241, 0.05);
            color: var(--primary-color);
        }
        
        .menu-item.active {
            background-color: rgba(99, 102, 241, 0.1);
            color: var(--primary-color);
            border-left-color: var(--primary-color);
        }
        
        .menu-item i {
            font-size: 1.25rem;
            margin-right: 0.75rem;
            width: 1.5rem;
            text-align: center;
        }
        
        .menu-item span {
            font-weight: 500;
        }
        
        .sidebar-footer {
            padding: 1.5rem;
            border-top: 1px solid var(--gray-light-color);
        }
        
        .user-profile {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: var(--border-radius-full);
            background-color: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 1rem;
        }
        
        .user-info {
            flex: 1;
        }
        
        .user-name {
            font-weight: 600;
            margin-bottom: 0.25rem;
            font-size: 0.875rem;
        }
        
        .user-email {
            font-size: 0.75rem;
            color: var(--gray-color);
        }
        
        .dropdown-toggle {
            background: transparent;
            border: none;
            color: var(--gray-color);
            cursor: pointer;
        }
        
        /* Main Content */
        .main-content {
            flex: 1;
            margin-left: 280px;
            padding: 2rem;
            transition: all 0.3s ease;
        }
        
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }
        
        .page-title {
            font-size: 1.75rem;
            margin-bottom: 0.5rem;
        }
        
        .page-subtitle {
            color: var(--gray-color);
            font-size: 1rem;
        }
        
        /* Filter and Search */
        .filter-row {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1.5rem;
            flex-wrap: wrap;
            gap: 1rem;
        }
        
        .search-box {
            position: relative;
            max-width: 300px;
            width: 100%;
        }
        
        .search-box input {
            padding: 0.75rem 1rem 0.75rem 2.5rem;
            border-radius: var(--border-radius);
            border: 1px solid var(--gray-light-color);
            width: 100%;
            font-size: 0.875rem;
        }
        
        .search-box i {
            position: absolute;
            left: 0.75rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--gray-color);
        }
        
        .filter-group {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            flex-wrap: wrap;
        }
        
        /* Table Styles */
        .table-card {
            background-color: var(--white-color);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow);
            overflow: hidden;
            margin-bottom: 2rem;
        }
        
        .table-responsive {
            overflow-x: auto;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .data-table th {
            background-color: var(--body-color);
            padding: 1rem;
            text-align: left;
            font-weight: 600;
            color: var(--dark-color);
            font-size: 0.875rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }
        
        .data-table td {
            padding: 1rem;
            border-top: 1px solid var(--gray-light-color);
            font-size: 0.875rem;
            vertical-align: middle;
        }
        
        .data-table tr:hover {
            background-color: rgba(99, 102, 241, 0.02);
        }
        
        .table-actions {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        /* Status Badges */
        .badge {
            padding: 0.35rem 0.75rem;
            border-radius: var(--border-radius-full);
            font-size: 0.75rem;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
        }
        
        .badge-success {
            background-color: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
        }
        
        .badge-warning {
            background-color: rgba(245, 158, 11, 0.1);
            color: var(--warning-color);
        }
        
        .badge-danger {
            background-color: rgba(239, 68, 68, 0.1);
            color: var(--danger-color);
        }
        
        .badge-info {
            background-color: rgba(59, 130, 246, 0.1);
            color: var(--info-color);
        }
        
        .badge-primary {
            background-color: rgba(99, 102, 241, 0.1);
            color: var(--primary-color);
        }
        
        /* Pagination */
        .pagination-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 1.5rem;
        }
        
        .pagination-info {
            font-size: 0.875rem;
            color: var(--gray-color);
        }
        
        .pagination {
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }
        
        .page-item {
            list-style: none;
        }
        
        .page-link {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 2.5rem;
            height: 2.5rem;
            border-radius: var(--border-radius);
            border: 1px solid var(--gray-light-color);
            color: var(--dark-color);
            font-size: 0.875rem;
            transition: all 0.3s ease;
        }
        
        .page-link:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
        }
        
        .page-item.active .page-link {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }
        
        .page-item.disabled .page-link {
            color: var(--gray-color);
            pointer-events: none;
        }
        
        /* Empty State */
        .empty-state {
            text-align: center;
            padding: 3rem 2rem;
        }
        
        .empty-state-icon {
            font-size: 3rem;
            color: var(--gray-color);
            margin-bottom: 1rem;
        }
        
        .empty-state-title {
            font-size: 1.25rem;
            margin-bottom: 0.5rem;
        }
        
        .empty-state-description {
            color: var(--gray-color);
            margin-bottom: 1.5rem;
            max-width: 400px;
            margin-left: auto;
            margin-right: auto;
        }
        
        /* Mobile Header (visible only on small screens) */
        .mobile-header {
            display: none;
        }
        
        /* Overlay for sidebar on mobile */
        .sidebar-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 999;
            display: none;
        }
        
        .sidebar-overlay.show {
            display: block;
        }
        
        /* Responsive */
        @media (max-width: 991.98px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0;
            }
            
            .sidebar-toggle {
                display: block;
            }
            
            .mobile-header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 1rem;
                background-color: var(--white-color);
                box-shadow: var(--shadow);
                position: sticky;
                top: 0;
                z-index: 999;
            }
            
            .filter-row {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .search-box {
                max-width: 100%;
            }
        }
        
        @media (max-width: 767.98px) {
            .page-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 1rem;
            }
            
            .pagination-container {
                flex-direction: column;
                gap: 1rem;
                align-items: flex-start;
            }
        }
    </style>
</head>
<body>
    <!-- Mobile Header (visible only on small screens) -->
    <div class="mobile-header">
        <button class="sidebar-toggle" id="sidebarToggle">
            <i class='bx bx-menu'></i>
        </button>
        <a href="index.html" class="navbar-brand">
            <span class="logo-text">Pay<span class="text-gradient">Soft</span></span>
        </a>
        <div class="dropdown">
            <button class="dropdown-toggle" type="button" id="mobileUserMenu" data-bs-toggle="dropdown" aria-expanded="false">
                <i class='bx bx-user-circle' style="font-size: 1.5rem;"></i>
            </button>
            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="mobileUserMenu">
                <li><a class="dropdown-item" href="profile.html">Mon profil</a></li>
                <li><a class="dropdown-item" href="settings.html">Paramètres</a></li>
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item" href="index.html">Déconnexion</a></li>
            </ul>
        </div>
    </div>

    <!-- Sidebar Overlay -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>

    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <a href="index.html" class="navbar-brand">
                    <span class="logo-text">Pay<span class="text-gradient">Soft</span></span>
                </a>
                <button class="sidebar-toggle d-lg-none" id="closeSidebar">
                    <i class='bx bx-x'></i>
                </button>
            </div>
            
            <div class="sidebar-menu">
                <div class="menu-item">
                    <i class='bx bxs-dashboard'></i>
                    <span>Tableau de bord</span>
                </div>
                
                <div class="menu-label">Paiements</div>
                
                <div class="menu-item active">
                    <i class='bx bx-link'></i>
                    <span>Liens de paiement</span>
                </div>
                
                <div class="menu-item">
                    <i class='bx bx-receipt'></i>
                    <span>Factures</span>
                </div>
                
                <div class="menu-item">
                    <i class='bx bx-refresh'></i>
                    <span>Abonnements</span>
                </div>
                
                <div class="menu-item">
                    <i class='bx bx-store'></i>
                    <span>Point de vente</span>
                </div>
                
                <div class="menu-label">Gestion</div>
                
                <div class="menu-item">
                    <i class='bx bx-user'></i>
                    <span>Clients</span>
                </div>
                
                <div class="menu-item">
                    <i class='bx bx-transfer'></i>
                    <span>Transactions</span>
                </div>
                
                <div class="menu-item">
                    <i class='bx bx-wallet'></i>
                    <span>Portefeuille</span>
                </div>
                
                <div class="menu-label">Compte</div>
                
                <div class="menu-item">
                    <i class='bx bx-cog'></i>
                    <span>Paramètres</span>
                </div>
                
                <div class="menu-item">
                    <i class='bx bx-help-circle'></i>
                    <span>Aide & Support</span>
                </div>
            </div>
            
            <div class="sidebar-footer">
                <div class="user-profile">
                    <div class="user-avatar">AM</div>
                    <div class="user-info">
                        <div class="user-name">Amadou Mbaye</div>
                        <div class="user-email"><EMAIL></div>
                    </div>
                    <div class="dropdown">
                        <button class="dropdown-toggle" type="button" id="userMenu" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class='bx bx-dots-vertical-rounded'></i>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userMenu">
                            <li><a class="dropdown-item" href="profile.html">Mon profil</a></li>
                            <li><a class="dropdown-item" href="settings.html">Paramètres</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="index.html">Déconnexion</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <div class="page-header">
                <div>
                    <h1 class="page-title">Liens de paiement</h1>
                    <p class="page-subtitle">Gérez tous vos liens de paiement et suivez leurs performances</p>
                </div>
                
                <div class="header-actions">
                    <a href="create-payment.html" class="btn btn-primary btn-icon">
                        <i class='bx bx-plus'></i>
                        <span>Créer un lien</span>
                    </a>
                </div>
            </div>
            
            <!-- Filter and Search -->
            <div class="filter-row">
                <div class="search-box">
                    <i class='bx bx-search'></i>
                    <input type="text" placeholder="Rechercher un lien..." id="searchInput">
                </div>
                
                <div class="filter-group">
                    <select class="form-select form-select-sm" id="statusFilter">
                        <option value="all">Tous les statuts</option>
                        <option value="active">Actif</option>
                        <option value="inactive">Inactif</option>
                        <option value="expired">Expiré</option>
                    </select>
                    
                    <select class="form-select form-select-sm" id="typeFilter">
                        <option value="all">Tous les types</option>
                        <option value="one-time">Paiement unique</option>
                        <option value="subscription">Abonnement</option>
                        <option value="donation">Don</option>
                    </select>
                    
                    <select class="form-select form-select-sm" id="dateFilter">
                        <option value="all">Toutes les dates</option>
                        <option value="today">Aujourd'hui</option>
                        <option value="week">Cette semaine</option>
                        <option value="month">Ce mois</option>
                        <option value="year">Cette année</option>
                    </select>
                </div>
            </div>
            
            <!-- Payment Links Table -->
            <div class="table-card">
                <div class="table-responsive">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Titre</th>
                                <th>Type</th>
                                <th>Montant</th>
                                <th>Statut</th>
                                <th>Paiements</th>
                                <th>Créé le</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="me-3" style="width: 40px; height: 40px; background-color: rgba(99, 102, 241, 0.1); border-radius: var(--border-radius); display: flex; align-items: center; justify-content: center;">
                                            <i class='bx bx-link' style="color: var(--primary-color);"></i>
                                        </div>
                                        <div>
                                            <div style="font-weight: 600;">Consultation</div>
                                            <div style="font-size: 0.75rem; color: var(--gray-color);">paysoft.com/p/consultation-25000</div>
                                        </div>
                                    </div>
                                </td>
                                <td>Paiement unique</td>
                                <td>25,000 FCFA</td>
                                <td><span class="badge badge-success">Actif</span></td>
                                <td>12</td>
                                <td>15 juin 2023</td>
                                <td>
                                    <div class="table-actions">
                                        <button class="btn btn-sm btn-icon-only btn-outline" title="Voir les détails">
                                            <i class='bx bx-show'></i>
                                        </button>
                                        <button class="btn btn-sm btn-icon-only btn-outline" title="Modifier">
                                            <i class='bx bx-edit'></i>
                                        </button>
                                        <button class="btn btn-sm btn-icon-only btn-outline" title="Copier le lien">
                                            <i class='bx bx-copy'></i>
                                        </button>
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-icon-only btn-outline dropdown-toggle" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                                <i class='bx bx-dots-vertical-rounded'></i>
                                            </button>
                                            <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton1">
                                                <li><a class="dropdown-item" href="#">Désactiver</a></li>
                                                <li><a class="dropdown-item" href="#">Dupliquer</a></li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li><a class="dropdown-item text-danger" href="#">Supprimer</a></li>
                                            </ul>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="me-3" style="width: 40px; height: 40px; background-color: rgba(99, 102, 241, 0.1); border-radius: var(--border-radius); display: flex; align-items: center; justify-content: center;">
                                            <i class='bx bx-refresh' style="color: var(--primary-color);"></i>
                                        </div>
                                        <div>
                                            <div style="font-weight: 600;">Abonnement Premium</div>
                                            <div style="font-size: 0.75rem; color: var(--gray-color);">paysoft.com/p/abonnement-premium-15000</div>
                                        </div>
                                    </div>
                                </td>
                                <td>Abonnement mensuel</td>
                                <td>15,000 FCFA</td>
                                <td><span class="badge badge-success">Actif</span></td>
                                <td>28</td>
                                <td>3 mai 2023</td>
                                <td>
                                    <div class="table-actions">
                                        <button class="btn btn-sm btn-icon-only btn-outline" title="Voir les détails">
                                            <i class='bx bx-show'></i>
                                        </button>
                                        <button class="btn btn-sm btn-icon-only btn-outline" title="Modifier">
                                            <i class='bx bx-edit'></i>
                                        </button>
                                        <button class="btn btn-sm btn-icon-only btn-outline" title="Copier le lien">
                                            <i class='bx bx-copy'></i>
                                        </button>
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-icon-only btn-outline dropdown-toggle" type="button" id="dropdownMenuButton2" data-bs-toggle="dropdown" aria-expanded="false">
                                                <i class='bx bx-dots-vertical-rounded'></i>
                                            </button>
                                            <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton2">
                                                <li><a class="dropdown-item" href="#">Désactiver</a></li>
                                                <li><a class="dropdown-item" href="#">Dupliquer</a></li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li><a class="dropdown-item text-danger" href="#">Supprimer</a></li>
                                            </ul>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="me-3" style="width: 40px; height: 40px; background-color: rgba(99, 102, 241, 0.1); border-radius: var(--border-radius); display: flex; align-items: center; justify-content: center;">
                                            <i class='bx bx-gift' style="color: var(--primary-color);"></i>
                                        </div>
                                        <div>
                                            <div style="font-weight: 600;">Don pour l'association</div>
                                            <div style="font-size: 0.75rem; color: var(--gray-color);">paysoft.com/p/don-association</div>
                                        </div>
                                    </div>
                                </td>
                                <td>Don (montant libre)</td>
                                <td>Variable</td>
                                <td><span class="badge badge-success">Actif</span></td>
                                <td>45</td>
                                <td>20 avril 2023</td>
                                <td>
                                    <div class="table-actions">
                                        <button class="btn btn-sm btn-icon-only btn-outline" title="Voir les détails">
                                            <i class='bx bx-show'></i>
                                        </button>
                                        <button class="btn btn-sm btn-icon-only btn-outline" title="Modifier">
                                            <i class='bx bx-edit'></i>
                                        </button>
                                        <button class="btn btn-sm btn-icon-only btn-outline" title="Copier le lien">
                                            <i class='bx bx-copy'></i>
                                        </button>
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-icon-only btn-outline dropdown-toggle" type="button" id="dropdownMenuButton3" data-bs-toggle="dropdown" aria-expanded="false">
                                                <i class='bx bx-dots-vertical-rounded'></i>
                                            </button>
                                            <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton3">
                                                <li><a class="dropdown-item" href="#">Désactiver</a></li>
                                                <li><a class="dropdown-item" href="#">Dupliquer</a></li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li><a class="dropdown-item text-danger" href="#">Supprimer</a></li>
                                            </ul>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="me-3" style="width: 40px; height: 40px; background-color: rgba(99, 102, 241, 0.1); border-radius: var(--border-radius); display: flex; align-items: center; justify-content: center;">
                                            <i class='bx bx-calendar-event' style="color: var(--primary-color);"></i>
                                        </div>
                                        <div>
                                            <div style="font-weight: 600;">Billet pour l'événement</div>
                                            <div style="font-size: 0.75rem; color: var(--gray-color);">paysoft.com/p/billet-evenement-5000</div>
                                        </div>
                                    </div>
                                </td>
                                <td>Paiement unique</td>
                                <td>5,000 FCFA</td>
                                <td><span class="badge badge-danger">Expiré</span></td>
                                <td>78</td>
                                <td>10 mars 2023</td>
                                <td>
                                    <div class="table-actions">
                                        <button class="btn btn-sm btn-icon-only btn-outline" title="Voir les détails">
                                            <i class='bx bx-show'></i>
                                        </button>
                                        <button class="btn btn-sm btn-icon-only btn-outline" title="Modifier">
                                            <i class='bx bx-edit'></i>
                                        </button>
                                        <button class="btn btn-sm btn-icon-only btn-outline" title="Copier le lien">
                                            <i class='bx bx-copy'></i>
                                        </button>
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-icon-only btn-outline dropdown-toggle" type="button" id="dropdownMenuButton4" data-bs-toggle="dropdown" aria-expanded="false">
                                                <i class='bx bx-dots-vertical-rounded'></i>
                                            </button>
                                            <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton4">
                                                <li><a class="dropdown-item" href="#">Réactiver</a></li>
                                                <li><a class="dropdown-item" href="#">Dupliquer</a></li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li><a class="dropdown-item text-danger" href="#">Supprimer</a></li>
                                            </ul>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="me-3" style="width: 40px; height: 40px; background-color: rgba(99, 102, 241, 0.1); border-radius: var(--border-radius); display: flex; align-items: center; justify-content: center;">
                                            <i class='bx bx-book' style="color: var(--primary-color);"></i>
                                        </div>
                                        <div>
                                            <div style="font-weight: 600;">Cours en ligne</div>
                                            <div style="font-size: 0.75rem; color: var(--gray-color);">paysoft.com/p/cours-en-ligne-30000</div>
                                        </div>
                                    </div>
                                </td>
                                <td>Paiement unique</td>
                                <td>30,000 FCFA</td>
                                <td><span class="badge badge-warning">Inactif</span></td>
                                <td>0</td>
                                <td>5 juin 2023</td>
                                <td>
                                    <div class="table-actions">
                                        <button class="btn btn-sm btn-icon-only btn-outline" title="Voir les détails">
                                            <i class='bx bx-show'></i>
                                        </button>
                                        <button class="btn btn-sm btn-icon-only btn-outline" title="Modifier">
                                            <i class='bx bx-edit'></i>
                                        </button>
                                        <button class="btn btn-sm btn-icon-only btn-outline" title="Copier le lien">
                                            <i class='bx bx-copy'></i>
                                        </button>
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-icon-only btn-outline dropdown-toggle" type="button" id="dropdownMenuButton5" data-bs-toggle="dropdown" aria-expanded="false">
                                                <i class='bx bx-dots-vertical-rounded'></i>
                                            </button>
                                            <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton5">
                                                <li><a class="dropdown-item" href="#">Activer</a></li>
                                                <li><a class="dropdown-item" href="#">Dupliquer</a></li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li><a class="dropdown-item text-danger" href="#">Supprimer</a></li>
                                            </ul>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <div class="pagination-container p-3">
                    <div class="pagination-info">
                        Affichage de 1 à 5 sur 12 liens
                    </div>
                    
                    <ul class="pagination">
                        <li class="page-item disabled">
                            <a class="page-link" href="#" aria-label="Previous">
                                <i class='bx bx-chevron-left'></i>
                            </a>
                        </li>
                        <li class="page-item active"><a class="page-link" href="#">1</a></li>
                        <li class="page-item"><a class="page-link" href="#">2</a></li>
                        <li class="page-item"><a class="page-link" href="#">3</a></li>
                        <li class="page-item">
                            <a class="page-link" href="#" aria-label="Next">
                                <i class='bx bx-chevron-right'></i>
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
            
            <!-- Empty State (hidden by default) -->
            <div class="empty-state" style="display: none;">
                <div class="empty-state-icon">
                    <i class='bx bx-link-alt'></i>
                </div>
                <h3 class="empty-state-title">Aucun lien de paiement trouvé</h3>
                <p class="empty-state-description">Vous n'avez pas encore créé de liens de paiement ou aucun lien ne correspond à vos critères de recherche.</p>
                <a href="create-payment.html" class="btn btn-primary btn-icon">
                    <i class='bx bx-plus'></i>
                    <span>Créer un lien</span>
                </a>
            </div>
        </main>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Sidebar Toggle
        const sidebarToggle = document.getElementById('sidebarToggle');
        const closeSidebar = document.getElementById('closeSidebar');
        const sidebar = document.getElementById('sidebar');
        const sidebarOverlay = document.getElementById('sidebarOverlay');
        
        if (sidebarToggle && sidebar) {
            sidebarToggle.addEventListener('click', function() {
                sidebar.classList.add('show');
                sidebarOverlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            });
        }
        
        if (closeSidebar && sidebar) {
            closeSidebar.addEventListener('click', function() {
                sidebar.classList.remove('show');
                sidebarOverlay.classList.remove('show');
                document.body.style.overflow = '';
            });
        }
        
        if (sidebarOverlay) {
            sidebarOverlay.addEventListener('click', function() {
                sidebar.classList.remove('show');
                sidebarOverlay.classList.remove('show');
                document.body.style.overflow = '';
            });
        }
        
        // Search and Filter Functionality
        const searchInput = document.getElementById('searchInput');
        const statusFilter = document.getElementById('statusFilter');
        const typeFilter = document.getElementById('typeFilter');
        const dateFilter = document.getElementById('dateFilter');
        const tableRows = document.querySelectorAll('.data-table tbody tr');
        const emptyState = document.querySelector('.empty-state');
        const tableCard = document.querySelector('.table-card');
        
        function filterTable() {
            const searchTerm = searchInput.value.toLowerCase();
            const status = statusFilter.value;
            const type = typeFilter.value;
            const date = dateFilter.value;
            
            let visibleRows = 0;
            
            tableRows.forEach(row => {
                const title = row.querySelector('td:first-child').textContent.toLowerCase();
                const rowType = row.querySelector('td:nth-child(2)').textContent.toLowerCase();
                const rowStatus = row.querySelector('.badge').textContent.toLowerCase();
                
                // Apply filters
                const matchesSearch = title.includes(searchTerm);
                const matchesStatus = status === 'all' || rowStatus.includes(status.toLowerCase());
                const matchesType = type === 'all' || rowType.includes(type.toLowerCase());
                // Date filter would require actual date comparison logic in a real app
                const matchesDate = date === 'all'; // Simplified for demo
                
                if (matchesSearch && matchesStatus && matchesType && matchesDate) {
                    row.style.display = '';
                    visibleRows++;
                } else {
                    row.style.display = 'none';
                }
            });
            
            // Show/hide empty state
            if (visibleRows === 0) {
                emptyState.style.display = 'block';
                tableCard.style.display = 'none';
            } else {
                emptyState.style.display = 'none';
                tableCard.style.display = 'block';
            }
        }
        
        // Add event listeners
        if (searchInput) searchInput.addEventListener('input', filterTable);
        if (statusFilter) statusFilter.addEventListener('change', filterTable);
        if (typeFilter) typeFilter.addEventListener('change', filterTable);
        if (dateFilter) dateFilter.addEventListener('change', filterTable);
        
        // Copy link functionality
        const copyButtons = document.querySelectorAll('.btn[title="Copier le lien"]');
        copyButtons.forEach(button => {
            button.addEventListener('click', function() {
                const linkText = this.closest('tr').querySelector('td:first-child div div:last-child div:last-child').textContent;
                navigator.clipboard.writeText(linkText).then(() => {
                    // Show success message
                    const originalIcon = this.innerHTML;
                    this.innerHTML = '<i class="bx bx-check"></i>';
                    setTimeout(() => {
                        this.innerHTML = originalIcon;
                    }, 2000);
                });
            });
        });
    </script>
</body>
</html>
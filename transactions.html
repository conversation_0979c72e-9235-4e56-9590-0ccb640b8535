<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Transactions - PaySoft</title>
    <meta name="description" content="<PERSON><PERSON><PERSON> et suivez toutes vos transactions sur la plateforme PaySoft.">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/boxicons@2.1.4/css/boxicons.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&family=Work+Sans:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #6366f1;
            --secondary-color: #4f46e5;
            --accent-color: #8b5cf6;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --info-color: #3b82f6;
            --dark-color: #1e293b;
            --gray-color: #64748b;
            --gray-light-color: #e2e8f0;
            --body-color: #f8fafc;
            --white-color: #ffffff;
            
            --primary-gradient: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
            
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
            
            --border-radius: 0.375rem;
            --border-radius-md: 0.5rem;
            --border-radius-lg: 0.75rem;
            --border-radius-xl: 1rem;
            --border-radius-2xl: 1.5rem;
            --border-radius-full: 9999px;
            
            --font-sans: 'Work Sans', sans-serif;
            --font-heading: 'Space Grotesk', sans-serif;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: var(--font-sans);
            color: var(--dark-color);
            background-color: var(--body-color);
            line-height: 1.6;
            overflow-x: hidden;
        }
        
        h1, h2, h3, h4, h5, h6 {
            font-family: var(--font-heading);
            font-weight: 700;
            line-height: 1.2;
        }
        
        a {
            text-decoration: none;
            color: var(--primary-color);
            transition: all 0.3s ease;
        }
        
        a:hover {
            color: var(--secondary-color);
        }
        
        .text-gradient {
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-fill-color: transparent;
        }
        
        .btn {
            padding: 0.75rem 1.5rem;
            border-radius: var(--border-radius);
            font-weight: 500;
            transition: all 0.3s ease;
            font-size: 1rem;
        }
        
        .btn-primary {
            background: var(--primary-gradient);
            border: none;
            color: white;
            box-shadow: 0 4px 6px rgba(99, 102, 241, 0.25);
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #5254cc 0%, #7e50dd 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 8px rgba(99, 102, 241, 0.3);
        }
        
        .btn-outline {
            border: 1.5px solid var(--gray-light-color);
            color: var(--dark-color);
            background: transparent;
        }
        
        .btn-outline:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
        }
        
        .btn-sm {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
        }
        
        .btn-icon {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }
        
        .btn-icon-only {
            width: 2.5rem;
            height: 2.5rem;
            padding: 0;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }
    /* Dashboard Layout */
        .dashboard-container {
            display: flex;
            min-height: 100vh;
        }
        
        /* Sidebar */
        .sidebar {
            width: 280px;
            background-color: var(--white-color);
            border-right: 1px solid var(--gray-light-color);
            height: 100vh;
            position: fixed;
            top: 0;
            left: 0;
            z-index: 1000;
            transition: all 0.3s ease;
            overflow-y: auto;
        }
        
        .sidebar-header {
            padding: 1.5rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 1px solid var(--gray-light-color);
        }
        
        .logo-text {
            font-family: var(--font-heading);
            font-size: 1.5rem;
            font-weight: 700;
        }
        
        .sidebar-toggle {
            display: none;
            background: transparent;
            border: none;
            color: var(--gray-color);
            font-size: 1.5rem;
            cursor: pointer;
        }
        
        .sidebar-menu {
            padding: 1.5rem 0;
        }
        
        .menu-label {
            font-size: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            color: var(--gray-color);
            padding: 0 1.5rem;
            margin-bottom: 0.75rem;
            margin-top: 1.5rem;
        }
        
        .menu-item {
            padding: 0.75rem 1.5rem;
            display: flex;
            align-items: center;
            color: var(--gray-color);
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
            cursor: pointer;
        }
        
        .menu-item:hover {
            background-color: rgba(99, 102, 241, 0.05);
            color: var(--primary-color);
        }
        
        .menu-item.active {
            background-color: rgba(99, 102, 241, 0.1);
            color: var(--primary-color);
            border-left-color: var(--primary-color);
        }
        
        .menu-item i {
            font-size: 1.25rem;
            margin-right: 0.75rem;
            width: 1.5rem;
            text-align: center;
        }
        
        .menu-item span {
            font-weight: 500;
        }
        
        .sidebar-footer {
            padding: 1.5rem;
            border-top: 1px solid var(--gray-light-color);
        }
        
        .user-profile {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: var(--border-radius-full);
            background-color: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 1rem;
        }
        
        .user-info {
            flex: 1;
        }
        
        .user-name {
            font-weight: 600;
            margin-bottom: 0.25rem;
            font-size: 0.875rem;
        }
        
        .user-email {
            font-size: 0.75rem;
            color: var(--gray-color);
        }
        
        .dropdown-toggle {
            background: transparent;
            border: none;
            color: var(--gray-color);
            cursor: pointer;
        }
        
        /* Main Content */
        .main-content {
            flex: 1;
            margin-left: 280px;
            padding: 2rem;
            transition: all 0.3s ease;
        }
        
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }
        
        .page-title {
            font-size: 1.75rem;
            margin-bottom: 0.5rem;
        }
        
        .page-subtitle {
            color: var(--gray-color);
            font-size: 1rem;
        }
        
        /* Filter and Search */
        .filter-row {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1.5rem;
            flex-wrap: wrap;
            gap: 1rem;
        }
        
        .search-box {
            position: relative;
            max-width: 300px;
            width: 100%;
        }
        
        .search-box input {
            padding: 0.75rem 1rem 0.75rem 2.5rem;
            border-radius: var(--border-radius);
            border: 1px solid var(--gray-light-color);
            width: 100%;
            font-size: 0.875rem;
        }
        
        .search-box i {
            position: absolute;
            left: 0.75rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--gray-color);
        }
        
        .filter-group {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            flex-wrap: wrap;
        }
        
        /* Table Styles */
        .table-card {
            background-color: var(--white-color);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow);
            overflow: hidden;
            margin-bottom: 2rem;
        }
        
        .table-responsive {
            overflow-x: auto;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .data-table th {
            background-color: var(--body-color);
            padding: 1rem;
            text-align: left;
            font-weight: 600;
            color: var(--dark-color);
            font-size: 0.875rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }
        
        .data-table td {
            padding: 1rem;
            border-top: 1px solid var(--gray-light-color);
            font-size: 0.875rem;
            vertical-align: middle;
        }
        
        .data-table tr:hover {
            background-color: rgba(99, 102, 241, 0.02);
        }
        
        .table-actions {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        /* Status Badges */
        .badge {
            padding: 0.35rem 0.75rem;
            border-radius: var(--border-radius-full);
            font-size: 0.75rem;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
        }
        
        .badge-success {
            background-color: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
        }
        
        .badge-warning {
            background-color: rgba(245, 158, 11, 0.1);
            color: var(--warning-color);
        }
        
        .badge-danger {
            background-color: rgba(239, 68, 68, 0.1);
            color: var(--danger-color);
        }
        
        .badge-info {
            background-color: rgba(59, 130, 246, 0.1);
            color: var(--info-color);
        }
        
        .badge-primary {
            background-color: rgba(99, 102, 241, 0.1);
            color: var(--primary-color);
        }
        
        /* Pagination */
        .pagination-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 1.5rem;
        }
        
        .pagination-info {
            font-size: 0.875rem;
            color: var(--gray-color);
        }
        
        .pagination {
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }
        
        .page-item {
            list-style: none;
        }
        
        .page-link {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 2.5rem;
            height: 2.5rem;
            border-radius: var(--border-radius);
            border: 1px solid var(--gray-light-color);
            color: var(--dark-color);
            font-size: 0.875rem;
            transition: all 0.3s ease;
        }
        
        .page-link:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
        }
        
        .page-item.active .page-link {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }
        
        .page-item.disabled .page-link {
            color: var(--gray-color);
            pointer-events: none;
        }
        
        /* Mobile Header (visible only on small screens) */
        .mobile-header {
            display: none;
        }
        
        /* Overlay for sidebar on mobile */
        .sidebar-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 999;
            display: none;
        }
        
        .sidebar-overlay.show {
            display: block;
        }
        
        /* Transaction Details */
        .transaction-icon {
            width: 40px;
            height: 40px;
            border-radius: var(--border-radius);
            background-color: rgba(99, 102, 241, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-color);
            font-size: 1.25rem;
        }
        
        /* Responsive */
        @media (max-width: 991.98px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0;
            }
            
            .sidebar-toggle {
                display: block;
            }
            
            .mobile-header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 1rem;
                background-color: var(--white-color);
                box-shadow: var(--shadow);
                position: sticky;
                top: 0;
                z-index: 999;
            }
            
            .filter-row {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .search-box {
                max-width: 100%;
            }
        }
        
        @media (max-width: 767.98px) {
            .page-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 1rem;
            }
            
            .pagination-container {
                flex-direction: column;
                gap: 1rem;
                align-items: flex-start;
            }
        }
    </style>
</head>
<body>
    <!-- Mobile Header (visible only on small screens) -->
    <div class="mobile-header">
        <button class="sidebar-toggle" id="sidebarToggle">
            <i class='bx bx-menu'></i>
        </button>
        <a href="index.html" class="navbar-brand">
            <span class="logo-text">Pay<span class="text-gradient">Soft</span></span>
        </a>
        <div class="dropdown">
            <button class="dropdown-toggle" type="button" id="mobileUserMenu" data-bs-toggle="dropdown" aria-expanded="false">
                <i class='bx bx-user-circle' style="font-size: 1.5rem;"></i>
            </button>
            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="mobileUserMenu">
                <li><a class="dropdown-item" href="profile.html">Mon profil</a></li>
                <li><a class="dropdown-item" href="settings.html">Paramètres</a></li>
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item" href="index.html">Déconnexion</a></li>
            </ul>
        </div>
    </div>

    <!-- Sidebar Overlay -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>

    <!-- Sidebar -->
    <aside class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <a href="index.html" class="navbar-brand">
                <span class="logo-text">Pay<span class="text-gradient">Soft</span></span>
            </a>
            <button class="sidebar-close" id="sidebarClose">
                <i class='bx bx-x'></i>
            </button>
        </div>
        <div class="sidebar-user">
            <div class="user-avatar">
                <img src="https://ui-avatars.com/api/?name=John+Doe&background=6366f1&color=fff" alt="John Doe">
            </div>
            <div class="user-info">
                <h5 class="user-name">John Doe</h5>
                <p class="user-business">Acme Inc.</p>
            </div>
        </div>
        <nav class="sidebar-nav">
            <ul class="nav-list">
                <li class="nav-item">
                    <a href="dashboard.html" class="nav-link">
                        <i class='bx bxs-dashboard'></i>
                        <span>Tableau de bord</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="create-payment.html" class="nav-link">
                        <i class='bx bx-plus-circle'></i>
                        <span>Créer un paiement</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="payment-links.html" class="nav-link">
                        <i class='bx bx-link'></i>
                        <span>Liens de paiement</span>
                    </a>
                </li>
                <li class="nav-item active">
                    <a href="transactions.html" class="nav-link">
                        <i class='bx bx-transfer'></i>
                        <span>Transactions</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="customers.html" class="nav-link">
                        <i class='bx bx-user'></i>
                        <span>Clients</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="settings.html" class="nav-link">
                        <i class='bx bx-cog'></i>
                        <span>Paramètres</span>
                    </a>
                </li>
            </ul>
        </nav>
        <div class="sidebar-footer">
            <a href="index.html" class="logout-link">
                <i class='bx bx-log-out'></i>
                <span>Déconnexion</span>
            </a>
        </div>
    </aside>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Page Header -->
        <div class="page-header">
            <div>
                <h1 class="page-title">Transactions</h1>
                <p class="page-subtitle">Gérez et suivez toutes vos transactions</p>
            </div>
            <div class="page-actions">
                <button class="btn btn-outline-primary">
                    <i class='bx bx-export'></i> Exporter
                </button>
            </div>
        </div>

        <!-- Filters and Search -->
        <div class="card">
            <div class="card-body">
                <div class="filter-row">
                    <div class="search-box">
                        <i class='bx bx-search'></i>
                        <input type="text" class="form-control" placeholder="Rechercher une transaction...">
                    </div>
                    <div class="filters">
                        <div class="filter-item">
                            <select class="form-select">
                                <option value="">Statut</option>
                                <option value="completed">Réussie</option>
                                <option value="pending">En attente</option>
                                <option value="failed">Échouée</option>
                                <option value="refunded">Remboursée</option>
                            </select>
                        </div>
                        <div class="filter-item">
                            <select class="form-select">
                                <option value="">Méthode de paiement</option>
                                <option value="mobile_money">Mobile Money</option>
                                <option value="card">Carte</option>
                                <option value="bank">Virement bancaire</option>
                            </select>
                        </div>
                        <div class="filter-item">
                            <input type="date" class="form-control" placeholder="Date">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Transactions Table -->
        <div class="card mt-4">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Client</th>
                                <th>Montant</th>
                                <th>Méthode</th>
                                <th>Statut</th>
                                <th>Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>TRX-2023-001</td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="transaction-icon">
                                            <i class='bx bx-user'></i>
                                        </div>
                                        <div class="ms-3">
                                            <h6 class="mb-0">Sophie Martin</h6>
                                            <small><EMAIL></small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="fw-semibold">15 000 XOF</div>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <i class='bx bx-mobile-alt me-1'></i> Orange Money
                                    </div>
                                </td>
                                <td>
                                    <span class="badge badge-success">Réussie</span>
                                </td>
                                <td>15 juin 2023, 14:30</td>
                                <td>
                                    <div class="d-flex gap-2">
                                        <button class="btn btn-sm btn-icon" title="Voir les détails">
                                            <i class='bx bx-show'></i>
                                        </button>
                                        <button class="btn btn-sm btn-icon" title="Télécharger le reçu">
                                            <i class='bx bx-download'></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>TRX-2023-002</td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="transaction-icon">
                                            <i class='bx bx-user'></i>
                                        </div>
                                        <div class="ms-3">
                                            <h6 class="mb-0">Amadou Diallo</h6>
                                            <small><EMAIL></small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="fw-semibold">25 000 XOF</div>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <i class='bx bx-credit-card me-1'></i> Visa
                                    </div>
                                </td>
                                <td>
                                    <span class="badge badge-warning">En attente</span>
                                </td>
                                <td>14 juin 2023, 10:15</td>
                                <td>
                                    <div class="d-flex gap-2">
                                        <button class="btn btn-sm btn-icon" title="Voir les détails">
                                            <i class='bx bx-show'></i>
                                        </button>
                                        <button class="btn btn-sm btn-icon" title="Télécharger le reçu">
                                            <i class='bx bx-download'></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>TRX-2023-003</td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="transaction-icon">
                                            <i class='bx bx-user'></i>
                                        </div>
                                        <div class="ms-3">
                                            <h6 class="mb-0">Fatou Ndiaye</h6>
                                            <small><EMAIL></small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="fw-semibold">7 500 XOF</div>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <i class='bx bx-mobile-alt me-1'></i> Wave
                                    </div>
                                </td>
                                <td>
                                    <span class="badge badge-success">Réussie</span>
                                </td>
                                <td>13 juin 2023, 16:45</td>
                                <td>
                                    <div class="d-flex gap-2">
                                        <button class="btn btn-sm btn-icon" title="Voir les détails">
                                            <i class='bx bx-show'></i>
                                        </button>
                                        <button class="btn btn-sm btn-icon" title="Télécharger le reçu">
                                            <i class='bx bx-download'></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>TRX-2023-004</td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="transaction-icon">
                                            <i class='bx bx-user'></i>
                                        </div>
                                        <div class="ms-3">
                                            <h6 class="mb-0">Kofi Mensah</h6>
                                            <small><EMAIL></small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="fw-semibold">30 000 XOF</div>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <i class='bx bx-credit-card me-1'></i> Mastercard
                                    </div>
                                </td>
                                <td>
                                    <span class="badge badge-danger">Échouée</span>
                                </td>
                                <td>12 juin 2023, 09:20</td>
                                <td>
                                    <div class="d-flex gap-2">
                                        <button class="btn btn-sm btn-icon" title="Voir les détails">
                                            <i class='bx bx-show'></i>
                                        </button>
                                        <button class="btn btn-sm btn-icon" title="Télécharger le reçu">
                                            <i class='bx bx-download'></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>TRX-2023-005</td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="transaction-icon">
                                            <i class='bx bx-user'></i>
                                        </div>
                                        <div class="ms-3">
                                            <h6 class="mb-0">Aya Touré</h6>
                                            <small><EMAIL></small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="fw-semibold">12 500 XOF</div>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <i class='bx bx-mobile-alt me-1'></i> MTN Mobile Money
                                    </div>
                                </td>
                                <td>
                                    <span class="badge badge-primary">Remboursée</span>
                                </td>
                                <td>11 juin 2023, 13:10</td>
                                <td>
                                    <div class="d-flex gap-2">
                                        <button class="btn btn-sm btn-icon" title="Voir les détails">
                                            <i class='bx bx-show'></i>
                                        </button>
                                        <button class="btn btn-sm btn-icon" title="Télécharger le reçu">
                                            <i class='bx bx-download'></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="pagination-container">
                    <div class="pagination-info">
                        Affichage de 1 à 5 sur 25 transactions
                    </div>
                    <nav aria-label="Page navigation">
                        <ul class="pagination">
                            <li class="page-item disabled">
                                <a class="page-link" href="#" aria-label="Previous">
                                    <i class='bx bx-chevron-left'></i>
                                </a>
                            </li>
                            <li class="page-item active"><a class="page-link" href="#">1</a></li>
                            <li class="page-item"><a class="page-link" href="#">2</a></li>
                            <li class="page-item"><a class="page-link" href="#">3</a></li>
                            <li class="page-item">
                                <a class="page-link" href="#" aria-label="Next">
                                    <i class='bx bx-chevron-right'></i>
                                </a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </main>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Toggle Sidebar
        const sidebar = document.getElementById('sidebar');
        const sidebarToggle = document.getElementById('sidebarToggle');
        const sidebarClose = document.getElementById('sidebarClose');
        const sidebarOverlay = document.getElementById('sidebarOverlay');

        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', () => {
                sidebar.classList.toggle('show');
                sidebarOverlay.classList.toggle('show');
            });
        }

        if (sidebarClose) {
            sidebarClose.addEventListener('click', () => {
                sidebar.classList.remove('show');
                sidebarOverlay.classList.remove('show');
            });
        }

        if (sidebarOverlay) {
            sidebarOverlay.addEventListener('click', () => {
                sidebar.classList.remove('show');
                sidebarOverlay.classList.remove('show');
            });
        }

        // Search functionality
        const searchInput = document.querySelector('.search-box input');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                // Implement search functionality here
                console.log('Searching for:', e.target.value);
            });
        }

        // Filter functionality
        const filterSelects = document.querySelectorAll('.filter-item select, .filter-item input');
        filterSelects.forEach(select => {
            select.addEventListener('change', (e) => {
                // Implement filter functionality here
                console.log('Filter changed:', e.target.value);
            });
        });
    </script>
</body>
</html>
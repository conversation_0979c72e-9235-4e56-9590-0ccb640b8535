/* ===== GENERAL STYLES ===== */
:root {
    /* Colors */
    --primary-color: #6366f1;
    --primary-gradient: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    --secondary-color: #10b981;
    --dark-color: #111827;
    --light-color: #f9fafb;
    --gray-color: #6b7280;
    --gray-light-color: #e5e7eb;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    
    /* Typography */
    --heading-font: 'Space Grotesk', sans-serif;
    --body-font: 'Work Sans', sans-serif;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* Border Radius */
    --border-radius-sm: 0.25rem;
    --border-radius: 0.5rem;
    --border-radius-md: 0.75rem;
    --border-radius-lg: 1rem;
    --border-radius-xl: 1.5rem;
    --border-radius-2xl: 2rem;
    --border-radius-full: 9999px;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--body-font);
    font-size: 1rem;
    line-height: 1.6;
    color: var(--dark-color);
    background-color: var(--light-color);
    overflow-x: hidden;
}

h1, h2, h3, h4, h5, h6 {
    font-family: var(--heading-font);
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1rem;
    color: var(--dark-color);
}

h1 {
    font-size: 3.5rem;
    letter-spacing: -0.02em;
}

h2 {
    font-size: 2.5rem;
    letter-spacing: -0.01em;
}

h3 {
    font-size: 2rem;
}

h4 {
    font-size: 1.5rem;
}

h5 {
    font-size: 1.25rem;
}

h6 {
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

p {
    margin-bottom: 1.5rem;
    color: var(--gray-color);
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: all 0.3s ease;
}

a:hover {
    color: var(--secondary-color);
}

img {
    max-width: 100%;
    height: auto;
}

section {
    padding: 100px 0;
    position: relative;
}

.container {
    max-width: 1200px;
    padding: 0 20px;
    margin: 0 auto;
}

.text-gradient {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
}

.section-title {
    margin-bottom: 60px;
}

.section-title h2 {
    margin-bottom: 20px;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
}

.section-title p {
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* ===== BUTTONS ===== */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
    cursor: pointer;
    font-size: 1rem;
    line-height: 1.5;
    text-align: center;
    text-decoration: none;
    border: none;
    outline: none;
}

.btn i {
    margin-right: 0.5rem;
    font-size: 1.25rem;
}

.btn-primary {
    background: var(--primary-gradient);
    color: white;
    box-shadow: 0 4px 15px rgba(99, 102, 241, 0.4);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(99, 102, 241, 0.6);
    color: white;
}

.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-outline:hover {
    background: var(--primary-color);
    color: white;
}

.btn-outline-secondary {
    background: transparent;
    color: var(--dark-color);
    border: 2px solid var(--gray-light-color);
}

.btn-outline-secondary:hover {
    background: var(--gray-light-color);
    color: var(--dark-color);
}

.btn-light {
    background: white;
    color: var(--primary-color);
    box-shadow: var(--shadow);
}

.btn-light:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
}

.btn-outline-light {
    background: transparent;
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.5);
}

.btn-outline-light:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: white;
}

/* ===== HEADER ===== */
.header-area {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 999;
    transition: all 0.3s ease;
}

.navbar {
    padding: 20px 0;
    transition: all 0.3s ease;
}

.navbar.sticky {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow);
    padding: 15px 0;
}

.navbar-brand {
    font-family: var(--heading-font);
    font-weight: 700;
    font-size: 1.75rem;
    color: var(--dark-color);
}

.navbar-toggler {
    border: none;
    background: transparent;
    font-size: 2rem;
    color: var(--dark-color);
    padding: 0;
}

.navbar-toggler:focus {
    box-shadow: none;
}

.nav-link {
    font-weight: 500;
    color: var(--dark-color);
    padding: 0.5rem 1rem;
    position: relative;
}

.nav-link:hover, .nav-link.active {
    color: var(--primary-color);
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 1rem;
    right: 1rem;
    height: 3px;
    background: var(--primary-gradient);
    border-radius: var(--border-radius-full);
}

.header-btn {
    margin-left: 1.5rem;
    display: flex;
    gap: 0.75rem;
}

/* ===== HERO SECTION ===== */
.hero-area {
    padding: 180px 0 100px;
    background-color: #f8fafc;
    position: relative;
    overflow: hidden;
}

.hero-content h1 {
    margin-bottom: 1.5rem;
}

.hero-content p {
    font-size: 1.125rem;
    margin-bottom: 2rem;
    max-width: 90%;
}

.hero-btns {
    display: flex;
    gap: 1rem;
    margin-bottom: 2.5rem;
}

.hero-stats {
    display: flex;
    gap: 2.5rem;
}

.stat-item h3 {
    font-size: 2rem;
    margin-bottom: 0.25rem;
    color: var(--primary-color);
}

.stat-item p {
    font-size: 0.875rem;
    margin-bottom: 0;
    color: var(--gray-color);
    font-weight: 500;
}

.hero-image {
    position: relative;
}

.floating-card {
    position: absolute;
    background: white;
    border-radius: var(--border-radius-md);
    padding: 1rem;
    box-shadow: var(--shadow-lg);
    display: flex;
    align-items: center;
    gap: 1rem;
    max-width: 250px;
    z-index: 2;
}

.payment-card {
    top: 20%;
    left: -10%;
    animation: float 6s ease-in-out infinite;
}

.feature-card {
    bottom: 15%;
    right: -5%;
    animation: float 6s ease-in-out infinite 2s;
}

@keyframes float {
    0% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-15px);
    }
    100% {
        transform: translateY(0);
    }
}

.card-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--border-radius);
    background: var(--primary-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
    flex-shrink: 0;
}

.feature-card .card-icon {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.card-text h5 {
    font-size: 1rem;
    margin-bottom: 0.25rem;
}

.card-text p {
    font-size: 0.875rem;
    margin-bottom: 0;
    color: var(--gray-color);
}

.shape-divider {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    overflow: hidden;
    line-height: 0;
}

.shape-divider svg {
    position: relative;
    display: block;
    width: calc(100% + 1.3px);
    height: 80px;
}

.shape-divider .shape-fill {
    fill: #FFFFFF;
}

/* ===== TRUSTED BY SECTION ===== */
.trusted-by {
    padding: 60px 0;
}

.trusted-logos {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    gap: 3rem;
    margin-top: 2rem;
}

.logo-item img {
    height: 30px;
    opacity: 0.7;
    transition: all 0.3s ease;
    filter: grayscale(100%);
}

.logo-item:hover img {
    opacity: 1;
    filter: grayscale(0%);
}

/* ===== FEATURES SECTION ===== */
.features-area {
    background-color: #f8fafc;
    position: relative;
}

.features-tabs {
    margin-top: 3rem;
}

.nav-pills {
    gap: 1rem;
    margin-bottom: 3rem;
}

.nav-pills .nav-link {
    background: white;
    border-radius: var(--border-radius);
    padding: 1rem 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: var(--gray-color);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-light-color);
}

.nav-pills .nav-link i {
    font-size: 1.25rem;
}

.nav-pills .nav-link.active {
    background: var(--primary-gradient);
    color: white;
    box-shadow: 0 4px 15px rgba(99, 102, 241, 0.4);
    border-color: transparent;
}

.feature-content {
    padding-right: 2rem;
}

.feature-content h3 {
    margin-bottom: 1.5rem;
}

.feature-list {
    list-style: none;
    margin: 2rem 0;
    padding: 0;
}

.feature-list li {
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: var(--gray-color);
}

.feature-list li i {
    color: var(--success-color);
    font-size: 1.25rem;
    flex-shrink: 0;
}

.feature-image {
    position: relative;
    z-index: 1;
}

.feature-image img {
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
}

/* ===== HOW IT WORKS SECTION ===== */
.how-it-works {
    background-color: white;
}

.steps-row {
    margin-top: 3rem;
}

.step-box {
    background: white;
    border-radius: var(--border-radius-lg);
    padding: 2.5rem 2rem;
    text-align: center;
    box-shadow: var(--shadow);
    position: relative;
    height: 100%;
    transition: all 0.3s ease;
    border: 1px solid var(--gray-light-color);
}

.step-box:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.step-number {
    position: absolute;
    top: -20px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 40px;
    background: var(--primary-gradient);
    color: white;
    border-radius: var(--border-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.25rem;
    box-shadow: 0 4px 10px rgba(99, 102, 241, 0.4);
}

.step-icon {
    width: 70px;
    height: 70px;
    background: rgba(99, 102, 241, 0.1);
    border-radius: var(--border-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: var(--primary-color);
    font-size: 2rem;
    transition: all 0.3s ease;
}

.step-box:hover .step-icon {
    background: var(--primary-gradient);
    color: white;
    transform: scale(1.1);
}

/* ===== PAYMENT METHODS SECTION ===== */
.payment-methods {
    background-color: #f8fafc;
}

.payment-category {
    background: white;
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    box-shadow: var(--shadow);
    margin-bottom: 2rem;
    height: 100%;
}

.payment-category h4 {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
    color: var(--dark-color);
}

.payment-category h4 i {
    color: var(--primary-color);
}

.payment-logos {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
}

.payment-logo {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    width: calc(20% - 1.5rem);
}

.payment-logo img {
    height: 40px;
    object-fit: contain;
}

.payment-logo span {
    font-size: 0.75rem;
    color: var(--gray-color);
    text-align: center;
}

.currencies-supported {
    background: white;
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    box-shadow: var(--shadow);
    margin-top: 2rem;
    text-align: center;
}

.currencies-supported h5 {
    margin-bottom: 1.5rem;
}

.currency-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    justify-content: center;
}

.currency-tag {
    background: rgba(99, 102, 241, 0.1);
    color: var(--primary-color);
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    font-weight: 600;
    font-size: 0.875rem;
}

/* ===== DEMO SECTION ===== */
.demo-section {
    background-color: white;
}

.demo-content h2 {
    margin-bottom: 1.5rem;
}

.demo-features {
    margin-top: 2rem;
}

.demo-feature {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.demo-feature i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

.demo-feature h5 {
    margin-bottom: 0.25rem;
}

.demo-feature p {
    margin-bottom: 0;
    font-size: 0.875rem;
}

.demo-video {
    position: relative;
}

.video-wrapper {
    position: relative;
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
}

.play-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80px;
    height: 80px;
    background: var(--primary-gradient);
    border-radius: var(--border-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
    cursor: pointer;
    box-shadow: 0 10px 30px rgba(99, 102, 241, 0.6);
    transition: all 0.3s ease;
}

.play-button:hover {
    transform: translate(-50%, -50%) scale(1.1);
}

/* ===== PRICING SECTION ===== */
.pricing-section {
    background-color: #f8fafc;
}

.pricing-toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 3rem;
}

.pricing-toggle span {
    font-weight: 600;
    color: var(--gray-color);
    cursor: pointer;
}

.pricing-toggle span.active {
    color: var(--primary-color);
}

.switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 30px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--gray-light-color);
    transition: .4s;
}

.slider:before {
    position: absolute;
    content: "";
    height: 22px;
    width: 22px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
}

input:checked + .slider {
    background: var(--primary-gradient);
}

input:checked + .slider:before {
    transform: translateX(30px);
}

.slider.round {
    border-radius: 34px;
}

.slider.round:before {
    border-radius: 50%;
}

.pricing-row {
    margin-top: 2rem;
}

.pricing-box {
    background: white;
    border-radius: var(--border-radius-lg);
    padding: 3rem 2rem;
    box-shadow: var(--shadow);
    position: relative;
    height: 100%;
    transition: all 0.3s ease;
    border: 1px solid var(--gray-light-color);
}

.pricing-box.featured {
    border-color: var(--primary-color);
    transform: scale(1.05);
    z-index: 1;
    box-shadow: var(--shadow-lg);
}

.pricing-box:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-10px);
}

.pricing-box.featured:hover {
    transform: scale(1.05) translateY(-10px);
}

.pricing-badge {
    position: absolute;
    top: -15px;
    right: 30px;
    background: var(--primary-gradient);
    color: white;
    padding: 0.5rem 1.5rem;
    border-radius: var(--border-radius);
    font-weight: 600;
    font-size: 0.875rem;
    box-shadow: 0 4px 10px rgba(99, 102, 241, 0.4);
}

.pricing-header {
    text-align: center;
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid var(--gray-light-color);
}

.pricing-header h3 {
    margin-bottom: 1.5rem;
}

.price-container {
    margin-bottom: 1rem;
}

.price {
    display: flex;
    align-items: baseline;
    justify-content: center;
    gap: 0.25rem;
}

.price .currency {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--dark-color);
}

.price .amount {
    font-size: 3.5rem;
    font-weight: 700;
    color: var(--primary-color);
    line-height: 1;
}

.price .period {
    font-size: 1rem;
    color: var(--gray-color);
}

.pricing-features ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.pricing-features li {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
    color: var(--gray-color);
}

.pricing-features li i {
    font-size: 1.25rem;
    flex-shrink: 0;
}

.pricing-features li i.bx-check {
    color: var(--success-color);
}

.pricing-features li i.bx-x {
    color: var(--gray-color);
}

.pricing-features li strong {
    color: var(--dark-color);
}

.pricing-footer {
    margin-top: 2rem;
    text-align: center;
}

.pricing-note {
    color: var(--gray-color);
    font-size: 0.875rem;
}

.link-with-arrow {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: var(--primary-color);
    margin-top: 1rem;
}

.link-with-arrow i {
    transition: transform 0.3s ease;
}

.link-with-arrow:hover i {
    transform: translateX(5px);
}

/* ===== TESTIMONIALS SECTION ===== */
.testimonials-section {
    background-color: white;
}

.testimonials-slider {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.testimonial-item {
    height: 100%;
}

.testimonial-content {
    background: white;
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    box-shadow: var(--shadow);
    height: 100%;
    transition: all 0.3s ease;
    border: 1px solid var(--gray-light-color);
}

.testimonial-content:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.testimonial-rating {
    margin-bottom: 1.5rem;
    color: #f59e0b;
    font-size: 1.25rem;
}

.testimonial-content p {
    font-style: italic;
    margin-bottom: 1.5rem;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.testimonial-author img {
    width: 50px;
    height: 50px;
    border-radius: var(--border-radius-full);
    object-fit: cover;
}

.testimonial-author h5 {
    margin-bottom: 0.25rem;
    font-size: 1rem;
}

.testimonial-author span {
    font-size: 0.875rem;
    color: var(--gray-color);
}

/* ===== FAQ SECTION ===== */
.faq-section {
    background-color: #f8fafc;
}

.accordion {
    margin-top: 3rem;
}

.accordion-item {
    margin-bottom: 1rem;
    border: 1px solid var(--gray-light-color);
    border-radius: var(--border-radius);
    overflow: hidden;
}

.accordion-button {
    font-weight: 600;
    padding: 1.25rem;
    background-color: white;
    color: var(--dark-color);
    box-shadow: none;
}

.accordion-button:not(.collapsed) {
    color: var(--primary-color);
    background-color: rgba(99, 102, 241, 0.05);
    box-shadow: none;
}

.accordion-button:focus {
    box-shadow: none;
    border-color: var(--primary-color);
}

.accordion-button::after {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%236366f1' class='bi bi-plus' viewBox='0 0 16 16'%3E%3Cpath d='M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z'/%3E%3C/svg%3E");
    transition: all 0.3s ease;
}

.accordion-button:not(.collapsed)::after {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%236366f1' class='bi bi-dash' viewBox='0 0 16 16'%3E%3Cpath d='M4 8a.5.5 0 0 1 .5-.5h7a.5.5 0 0 1 0 1h-7A.5.5 0 0 1 4 8z'/%3E%3C/svg%3E");
}

.accordion-body {
    padding: 1.25rem;
    color: var(--gray-color);
}

/* ===== CTA SECTION ===== */
.cta-section {
    padding: 80px 0;
}

.cta-box {
    background: var(--primary-gradient);
    border-radius: var(--border-radius-lg);
    padding: 4rem;
    color: white;
    box-shadow: var(--shadow-lg);
}

.cta-box h2 {
    color: white;
    margin-bottom: 1rem;
}

.cta-box p {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 0;
    font-size: 1.125rem;
}

/* ===== FOOTER ===== */
.footer-area {
    background-color: var(--dark-color);
    color: white;
    padding: 80px 0 40px;
}

.footer-widget {
    margin-bottom: 2.5rem;
}

.footer-logo {
    display: inline-block;
    margin-bottom: 1.5rem;
    font-family: var(--heading-font);
    font-weight: 700;
    font-size: 1.75rem;
    color: white;
}

.footer-widget p {
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 1.5rem;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    width: 40px;
    height: 40px;
    border-radius: var(--border-radius-full);
    background: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background: var(--primary-color);
    transform: translateY(-5px);
}

.footer-widget h4 {
    color: white;
    margin-bottom: 1.5rem;
    font-size: 1.25rem;
}

.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links li {
    margin-bottom: 0.75rem;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.7);
    transition: all 0.3s ease;
}

.footer-links a:hover {
    color: white;
    padding-left: 5px;
}

.footer-bottom {
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-bottom p {
    margin-bottom: 0;
    color: rgba(255, 255, 255, 0.5);
    font-size: 0.875rem;
}

.footer-bottom-links {
    display: flex;
    gap: 1.5rem;
}

.footer-bottom-links a {
    color: rgba(255, 255, 255, 0.5);
    font-size: 0.875rem;
}

.footer-bottom-links a:hover {
    color: white;
}

/* ===== BACK TO TOP BUTTON ===== */
.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: var(--primary-gradient);
    color: white;
    border-radius: var(--border-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    box-shadow: var(--shadow-lg);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 99;
}

.back-to-top.active {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    transform: translateY(-5px);
}

/* ===== RESPONSIVE STYLES ===== */
@media (max-width: 1199.98px) {
    h1 {
        font-size: 3rem;
    }
    
    h2 {
        font-size: 2.25rem;
    }
    
    .hero-area {
        padding: 150px 0 80px;
    }
    
    .floating-card {
        max-width: 220px;
    }
}

@media (max-width: 991.98px) {
    h1 {
        font-size: 2.5rem;
    }
    
    h2 {
        font-size: 2rem;
    }
    
    section {
        padding: 80px 0;
    }
    
    .navbar-collapse {
        background: white;
        padding: 1.5rem;
        border-radius: var(--border-radius);
        box-shadow: var(--shadow);
        margin-top: 1rem;
    }
    
    .header-btn {
        margin-left: 0;
        margin-top: 1rem;
    }
    
    .hero-area {
        padding: 120px 0 60px;
    }
    
    .hero-content {
        margin-bottom: 3rem;
    }
    
    .hero-stats {
        justify-content: space-between;
    }
    
    .floating-card {
        position: relative;
        top: auto;
        left: auto;
        right: auto;
        bottom: auto;
        margin: 1rem 0;
        max-width: 100%;
    }
    
    .feature-content {
        padding-right: 0;
        margin-bottom: 2rem;
    }
    
    .pricing-box.featured {
        transform: scale(1);
    }
    
    .pricing-box.featured:hover {
        transform: translateY(-10px);
    }
    
    .cta-box {
        padding: 3rem 2rem;
        text-align: center;
    }
    
    .cta-box .text-lg-end {
        text-align: center !important;
        margin-top: 1.5rem;
    }
}

@media (max-width: 767.98px) {
    h1 {
        font-size: 2.25rem;
    }
    
    h2 {
        font-size: 1.75rem;
    }
    
    section {
        padding: 60px 0;
    }
    
    .section-title {
        margin-bottom: 40px;
    }
    
    .hero-btns {
        flex-direction: column;
        gap: 1rem;
    }
    
    .hero-btns .btn {
        width: 100%;
    }
    
    .hero-stats {
        flex-wrap: wrap;
        gap: 1.5rem;
    }
    
    .nav-pills {
        flex-wrap: wrap;
    }
    
    .nav-pills .nav-link {
        width: 100%;
    }
    
    .payment-logo {
        width: calc(33.333% - 1rem);
    }
    
    .testimonials-slider {
        grid-template-columns: 1fr;
    }
    
    .footer-bottom-links {
        flex-direction: column;
        gap: 0.5rem;
        margin-top: 1rem;
    }
    
    .footer-bottom .text-md-end {
        text-align: left !important;
    }
}

@media (max-width: 575.98px) {
    h1 {
        font-size: 2rem;
    }
    
    h2 {
        font-size: 1.5rem;
    }
    
    .payment-logo {
        width: calc(50% - 0.75rem);
    }
}
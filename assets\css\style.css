/* ===== COMMON STYLES FOR ALL PAGES ===== */

/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Work Sans', sans-serif;
    line-height: 1.6;
    color: #111827;
    background-color: #ffffff;
    overflow-x: hidden;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Space Grotesk', sans-serif;
    font-weight: 700;
    line-height: 1.2;
    color: #111827;
}

/* Common utility classes */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

@media (min-width: 1024px) {
    .container {
        padding: 0 2rem;
    }
}

/* Common animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Common transition classes */
.transition-all {
    transition: all 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-2px);
}

/* Focus styles for accessibility */
.focus-ring:focus {
    outline: 2px solid #0ea5e9;
    outline-offset: 2px;
}

/* Common button styles that work with TailwindCSS */
.btn-gradient {
    background: linear-gradient(135deg, #0ea5e9, #22c55e);
    transition: all 0.3s ease;
}

.btn-gradient:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(14, 165, 233, 0.3);
}

/* Loading states */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Common card styles */
.card-hover {
    transition: all 0.3s ease;
}

.card-hover:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

/* Responsive utilities */
@media (max-width: 640px) {
    .container {
        padding: 0 1rem;
    }
}

@media (min-width: 1024px) {
    .container {
        padding: 0 2rem;
    }
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }
}

/* Back to top button */
.back-to-top {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 3rem;
    height: 3rem;
    background: linear-gradient(135deg, #0ea5e9, #22c55e);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;
}

.back-to-top.active {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(14, 165, 233, 0.3);
}

/* Form styles for compatibility */
.form-control {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: #0ea5e9;
    box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #374151;
}

/* Error and success messages */
.alert {
    padding: 1rem;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
}

.alert-success {
    background-color: #d1fae5;
    color: #065f46;
    border: 1px solid #a7f3d0;
}

.alert-error {
    background-color: #fee2e2;
    color: #991b1b;
    border: 1px solid #fca5a5;
}

.alert-warning {
    background-color: #fef3c7;
    color: #92400e;
    border: 1px solid #fde68a;
}

.alert-info {
    background-color: #dbeafe;
    color: #1e40af;
    border: 1px solid #93c5fd;
}

/* Accessibility improvements */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Skip to content link */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: #000;
    color: #fff;
    padding: 8px;
    text-decoration: none;
    z-index: 1000;
}

.skip-link:focus {
    top: 6px;
}

/* End of common styles */









/* ===== TESTIMONIALS SECTION ===== */
.testimonials-section {
    background-color: white;
}

.testimonials-slider {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.testimonial-item {
    height: 100%;
}

.testimonial-content {
    background: white;
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    box-shadow: var(--shadow);
    height: 100%;
    transition: all 0.3s ease;
    border: 1px solid var(--gray-light-color);
}

.testimonial-content:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.testimonial-rating {
    margin-bottom: 1.5rem;
    color: #f59e0b;
    font-size: 1.25rem;
}

.testimonial-content p {
    font-style: italic;
    margin-bottom: 1.5rem;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.testimonial-author img {
    width: 50px;
    height: 50px;
    border-radius: var(--border-radius-full);
    object-fit: cover;
}

.testimonial-author h5 {
    margin-bottom: 0.25rem;
    font-size: 1rem;
}

.testimonial-author span {
    font-size: 0.875rem;
    color: var(--gray-color);
}

/* ===== FAQ SECTION ===== */
.faq-section {
    background-color: #f8fafc;
}

.accordion {
    margin-top: 3rem;
}

.accordion-item {
    margin-bottom: 1rem;
    border: 1px solid var(--gray-light-color);
    border-radius: var(--border-radius);
    overflow: hidden;
}

.accordion-button {
    font-weight: 600;
    padding: 1.25rem;
    background-color: white;
    color: var(--dark-color);
    box-shadow: none;
}

.accordion-button:not(.collapsed) {
    color: var(--primary-color);
    background-color: rgba(99, 102, 241, 0.05);
    box-shadow: none;
}

.accordion-button:focus {
    box-shadow: none;
    border-color: var(--primary-color);
}

.accordion-button::after {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%236366f1' class='bi bi-plus' viewBox='0 0 16 16'%3E%3Cpath d='M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z'/%3E%3C/svg%3E");
    transition: all 0.3s ease;
}

.accordion-button:not(.collapsed)::after {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%236366f1' class='bi bi-dash' viewBox='0 0 16 16'%3E%3Cpath d='M4 8a.5.5 0 0 1 .5-.5h7a.5.5 0 0 1 0 1h-7A.5.5 0 0 1 4 8z'/%3E%3C/svg%3E");
}

.accordion-body {
    padding: 1.25rem;
    color: var(--gray-color);
}

/* ===== CTA SECTION ===== */
.cta-section {
    padding: 80px 0;
}

.cta-box {
    background: var(--primary-gradient);
    border-radius: var(--border-radius-lg);
    padding: 4rem;
    color: white;
    box-shadow: var(--shadow-lg);
}

.cta-box h2 {
    color: white;
    margin-bottom: 1rem;
}

.cta-box p {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 0;
    font-size: 1.125rem;
}

/* ===== FOOTER ===== */
.footer-area {
    background-color: var(--dark-color);
    color: white;
    padding: 80px 0 40px;
}

.footer-widget {
    margin-bottom: 2.5rem;
}

.footer-logo {
    display: inline-block;
    margin-bottom: 1.5rem;
    font-family: var(--heading-font);
    font-weight: 700;
    font-size: 1.75rem;
    color: white;
}

.footer-widget p {
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 1.5rem;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    width: 40px;
    height: 40px;
    border-radius: var(--border-radius-full);
    background: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background: var(--primary-color);
    transform: translateY(-5px);
}

.footer-widget h4 {
    color: white;
    margin-bottom: 1.5rem;
    font-size: 1.25rem;
}

.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links li {
    margin-bottom: 0.75rem;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.7);
    transition: all 0.3s ease;
}

.footer-links a:hover {
    color: white;
    padding-left: 5px;
}

.footer-bottom {
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-bottom p {
    margin-bottom: 0;
    color: rgba(255, 255, 255, 0.5);
    font-size: 0.875rem;
}

.footer-bottom-links {
    display: flex;
    gap: 1.5rem;
}

.footer-bottom-links a {
    color: rgba(255, 255, 255, 0.5);
    font-size: 0.875rem;
}

.footer-bottom-links a:hover {
    color: white;
}

/* ===== BACK TO TOP BUTTON ===== */
.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: var(--primary-gradient);
    color: white;
    border-radius: var(--border-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    box-shadow: var(--shadow-lg);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 99;
}

.back-to-top.active {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    transform: translateY(-5px);
}

/* ===== RESPONSIVE STYLES ===== */
@media (max-width: 1199.98px) {
    h1 {
        font-size: 3rem;
    }
    
    h2 {
        font-size: 2.25rem;
    }
    
    .hero-area {
        padding: 150px 0 80px;
    }
    
    .floating-card {
        max-width: 220px;
    }
}

@media (max-width: 991.98px) {
    h1 {
        font-size: 2.5rem;
    }
    
    h2 {
        font-size: 2rem;
    }
    
    section {
        padding: 80px 0;
    }
    
    .navbar-collapse {
        background: white;
        padding: 1.5rem;
        border-radius: var(--border-radius);
        box-shadow: var(--shadow);
        margin-top: 1rem;
    }
    
    .header-btn {
        margin-left: 0;
        margin-top: 1rem;
    }
    
    .hero-area {
        padding: 120px 0 60px;
    }
    
    .hero-content {
        margin-bottom: 3rem;
    }
    
    .hero-stats {
        justify-content: space-between;
    }
    
    .floating-card {
        position: relative;
        top: auto;
        left: auto;
        right: auto;
        bottom: auto;
        margin: 1rem 0;
        max-width: 100%;
    }
    
    .feature-content {
        padding-right: 0;
        margin-bottom: 2rem;
    }
    
    .pricing-box.featured {
        transform: scale(1);
    }
    
    .pricing-box.featured:hover {
        transform: translateY(-10px);
    }
    
    .cta-box {
        padding: 3rem 2rem;
        text-align: center;
    }
    
    .cta-box .text-lg-end {
        text-align: center !important;
        margin-top: 1.5rem;
    }
}

@media (max-width: 767.98px) {
    h1 {
        font-size: 2.25rem;
    }
    
    h2 {
        font-size: 1.75rem;
    }
    
    section {
        padding: 60px 0;
    }
    
    .section-title {
        margin-bottom: 40px;
    }
    
    .hero-btns {
        flex-direction: column;
        gap: 1rem;
    }
    
    .hero-btns .btn {
        width: 100%;
    }
    
    .hero-stats {
        flex-wrap: wrap;
        gap: 1.5rem;
    }
    
    .nav-pills {
        flex-wrap: wrap;
    }
    
    .nav-pills .nav-link {
        width: 100%;
    }
    
    .payment-logo {
        width: calc(33.333% - 1rem);
    }
    
    .testimonials-slider {
        grid-template-columns: 1fr;
    }
    
    .footer-bottom-links {
        flex-direction: column;
        gap: 0.5rem;
        margin-top: 1rem;
    }
    
    .footer-bottom .text-md-end {
        text-align: left !important;
    }
}

@media (max-width: 575.98px) {
    h1 {
        font-size: 2rem;
    }
    
    h2 {
        font-size: 1.5rem;
    }
    
    .payment-logo {
        width: calc(50% - 0.75rem);
    }
}
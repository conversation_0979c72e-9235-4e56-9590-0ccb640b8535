<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Paramètres | PaySoft - Plateforme de paiement panafricaine</title>
    <meta name="description" content="Configurez votre compte et personnalisez vos préférences sur PaySoft, la plateforme de paiement panafricaine.">
    
    <!-- CDN Links -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        /* General Styles */
        :root {
            --primary-color: #6366f1;
            --primary-dark-color: #4f46e5;
            --secondary-color: #f59e0b;
            --dark-color: #111827;
            --gray-color: #6b7280;
            --gray-light-color: #e5e7eb;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --white-color: #ffffff;
            --border-radius: 0.5rem;
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f9fafb;
            color: var(--dark-color);
            line-height: 1.6;
        }
        
        /* Typography */
        h1, h2, h3, h4, h5, h6 {
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .text-gradient {
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }
        
        /* Shadows and Border Radius */
        .card {
            border-radius: var(--border-radius);
            border: none;
            box-shadow: var(--shadow);
            margin-bottom: 1.5rem;
        }
        
        /* Buttons */
        .btn {
            border-radius: var(--border-radius);
            padding: 0.5rem 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .btn-primary:hover {
            background-color: var(--primary-dark-color);
            border-color: var(--primary-dark-color);
        }
        
        .btn-outline-primary {
            color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .btn-outline-primary:hover {
            background-color: var(--primary-color);
            color: white;
        }
        
        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }
        
        .btn-icon {
            width: 2rem;
            height: 2rem;
            padding: 0;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background-color: rgba(99, 102, 241, 0.1);
            color: var(--primary-color);
            border: none;
        }
        
        .btn-icon:hover {
            background-color: rgba(99, 102, 241, 0.2);
        }
        
        /* Dashboard Layout */
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            width: 250px;
            height: 100vh;
            background-color: var(--white-color);
            box-shadow: var(--shadow);
            z-index: 1000;
            transition: transform 0.3s ease;
        }
        
        .sidebar-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1.5rem 1rem;
            border-bottom: 1px solid var(--gray-light-color);
        }
        
        .sidebar-close {
            display: none;
            background: none;
            border: none;
            font-size: 1.5rem;
            color: var(--gray-color);
            cursor: pointer;
        }
        
        .sidebar-user {
            display: flex;
            align-items: center;
            padding: 1rem;
            border-bottom: 1px solid var(--gray-light-color);
        }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            overflow: hidden;
            margin-right: 0.75rem;
        }
        
        .user-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .user-name {
            font-size: 0.875rem;
            font-weight: 600;
            margin-bottom: 0;
        }
        
        .user-business {
            font-size: 0.75rem;
            color: var(--gray-color);
            margin-bottom: 0;
        }
        
        .sidebar-nav {
            padding: 1rem 0;
        }
        
        .nav-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .nav-item {
            margin-bottom: 0.25rem;
        }
        
        .nav-link {
            display: flex;
            align-items: center;
            padding: 0.75rem 1.5rem;
            color: var(--gray-color);
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }
        
        .nav-link i {
            margin-right: 0.75rem;
            font-size: 1.25rem;
        }
        
        .nav-link:hover {
            color: var(--primary-color);
            background-color: rgba(99, 102, 241, 0.1);
        }
        
        .nav-item.active .nav-link {
            color: var(--primary-color);
            background-color: rgba(99, 102, 241, 0.1);
            border-left-color: var(--primary-color);
        }
        
        .sidebar-footer {
            padding: 1rem 1.5rem;
            border-top: 1px solid var(--gray-light-color);
            position: absolute;
            bottom: 0;
            width: 100%;
        }
        
        .logout-link {
            display: flex;
            align-items: center;
            color: var(--gray-color);
            text-decoration: none;
        }
        
        .logout-link i {
            margin-right: 0.75rem;
        }
        
        .logout-link:hover {
            color: var(--danger-color);
        }
        
        .main-content {
            margin-left: 250px;
            padding: 2rem;
            min-height: 100vh;
        }
        
        /* Page Header */
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }
        
        .page-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.25rem;
        }
        
        .page-subtitle {
            color: var(--gray-color);
            margin-bottom: 0;
        }
        
        /* Settings Tabs */
        .settings-tabs {
            margin-bottom: 2rem;
        }
        
        .nav-tabs {
            border-bottom: 1px solid var(--gray-light-color);
        }
        
        .nav-tabs .nav-link {
            border: none;
            border-bottom: 2px solid transparent;
            color: var(--gray-color);
            font-weight: 500;
            padding: 0.75rem 1rem;
            margin-right: 1rem;
            transition: all 0.3s ease;
        }
        
        .nav-tabs .nav-link:hover {
            color: var(--primary-color);
            border-bottom-color: var(--primary-color);
        }
        
        .nav-tabs .nav-link.active {
            color: var(--primary-color);
            border-bottom-color: var(--primary-color);
            background-color: transparent;
        }
        
        /* Form Styles */
        .form-label {
            font-weight: 500;
            margin-bottom: 0.5rem;
        }
        
        .form-control {
            border-radius: var(--border-radius);
            border: 1px solid var(--gray-light-color);
            padding: 0.625rem 0.75rem;
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.25rem rgba(99, 102, 241, 0.25);
        }
        
        .form-text {
            font-size: 0.75rem;
            color: var(--gray-color);
        }
        
        .form-check-input:checked {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        /* Settings Cards */
        .settings-card {
            margin-bottom: 2rem;
        }
        
        .settings-card-header {
            padding: 1.25rem 1.5rem;
            border-bottom: 1px solid var(--gray-light-color);
        }
        
        .settings-card-title {
            font-size: 1.125rem;
            font-weight: 600;
            margin-bottom: 0.25rem;
        }
        
        .settings-card-description {
            color: var(--gray-color);
            margin-bottom: 0;
            font-size: 0.875rem;
        }
        
        .settings-card-body {
            padding: 1.5rem;
        }
        
        /* Mobile Header (visible only on small screens) */
        .mobile-header {
            display: none;
        }
        
        /* Overlay for sidebar on mobile */
        .sidebar-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 999;
            display: none;
        }
        
        .sidebar-overlay.show {
            display: block;
        }
        
        /* Toggle Switch */
        .form-switch {
            padding-left: 2.5rem;
        }
        
        .form-switch .form-check-input {
            width: 2rem;
            height: 1rem;
            margin-left: -2.5rem;
        }
        
        /* Responsive */
        @media (max-width: 991.98px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0;
            }
            
            .sidebar-toggle {
                display: block;
            }
            
            .mobile-header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 1rem;
                background-color: var(--white-color);
                box-shadow: var(--shadow);
                position: sticky;
                top: 0;
                z-index: 999;
            }
        }
        
        @media (max-width: 767.98px) {
            .page-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 1rem;
            }
            
            .settings-tabs {
                overflow-x: auto;
                white-space: nowrap;
                width: 100%;
            }
            
            .nav-tabs {
                display: flex;
                flex-wrap: nowrap;
            }
        }
    </style>
</head>
<body>
    <!-- Mobile Header (visible only on small screens) -->
    <div class="mobile-header">
        <button class="sidebar-toggle" id="sidebarToggle">
            <i class='bx bx-menu'></i>
        </button>
        <a href="index.html" class="navbar-brand">
            <span class="logo-text">Pay<span class="text-gradient">Soft</span></span>
        </a>
        <div class="dropdown">
            <button class="dropdown-toggle" type="button" id="mobileUserMenu" data-bs-toggle="dropdown" aria-expanded="false">
                <i class='bx bx-user-circle' style="font-size: 1.5rem;"></i>
            </button>
            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="mobileUserMenu">
                <li><a class="dropdown-item" href="profile.html">Mon profil</a></li>
                <li><a class="dropdown-item" href="settings.html">Paramètres</a></li>
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item" href="index.html">Déconnexion</a></li>
            </ul>
        </div>
    </div>

    <!-- Sidebar Overlay -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>

    <!-- Sidebar -->
    <aside class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <a href="index.html" class="navbar-brand">
                <span class="logo-text">Pay<span class="text-gradient">Soft</span></span>
            </a>
            <button class="sidebar-close" id="sidebarClose">
                <i class='bx bx-x'></i>
            </button>
        </div>
        <div class="sidebar-user">
            <div class="user-avatar">
                <img src="https://ui-avatars.com/api/?name=John+Doe&background=6366f1&color=fff" alt="John Doe">
            </div>
            <div class="user-info">
                <h5 class="user-name">John Doe</h5>
                <p class="user-business">Acme Inc.</p>
            </div>
        </div>
        <nav class="sidebar-nav">
            <ul class="nav-list">
                <li class="nav-item">
                    <a href="dashboard.html" class="nav-link">
                        <i class='bx bxs-dashboard'></i>
                        <span>Tableau de bord</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="create-payment.html" class="nav-link">
                        <i class='bx bx-plus-circle'></i>
                        <span>Créer un paiement</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="payment-links.html" class="nav-link">
                        <i class='bx bx-link'></i>
                        <span>Liens de paiement</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="transactions.html" class="nav-link">
                        <i class='bx bx-transfer'></i>
                        <span>Transactions</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="customers.html" class="nav-link">
                        <i class='bx bx-user'></i>
                        <span>Clients</span>
                    </a>
                </li>
                <li class="nav-item active">
                    <a href="settings.html" class="nav-link">
                        <i class='bx bx-cog'></i>
                        <span>Paramètres</span>
                    </a>
                </li>
            </ul>
        </nav>
        <div class="sidebar-footer">
            <a href="index.html" class="logout-link">
                <i class='bx bx-log-out'></i>
                <span>Déconnexion</span>
            </a>
        </div>
    </aside>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Page Header -->
        <div class="page-header">
            <div>
                <h1 class="page-title">Paramètres</h1>
                <p class="page-subtitle">Gérez votre compte et vos préférences</p>
            </div>
        </div>

        <!-- Settings Tabs -->
        <div class="settings-tabs">
            <ul class="nav nav-tabs" id="settingsTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="profile-tab" data-bs-toggle="tab" data-bs-target="#profile" type="button" role="tab" aria-controls="profile" aria-selected="true">
                        <i class='bx bx-user me-2'></i> Profil
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="business-tab" data-bs-toggle="tab" data-bs-target="#business" type="button" role="tab" aria-controls="business" aria-selected="false">
                        <i class='bx bx-building-house me-2'></i> Entreprise
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="payment-tab" data-bs-toggle="tab" data-bs-target="#payment" type="button" role="tab" aria-controls="payment" aria-selected="false">
                        <i class='bx bx-credit-card me-2'></i> Paiement
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="security-tab" data-bs-toggle="tab" data-bs-target="#security" type="button" role="tab" aria-controls="security" aria-selected="false">
                        <i class='bx bx-lock-alt me-2'></i> Sécurité
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="notifications-tab" data-bs-toggle="tab" data-bs-target="#notifications" type="button" role="tab" aria-controls="notifications" aria-selected="false">
                        <i class='bx bx-bell me-2'></i> Notifications
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="api-tab" data-bs-toggle="tab" data-bs-target="#api" type="button" role="tab" aria-controls="api" aria-selected="false">
                        <i class='bx bx-code-alt me-2'></i> API
                    </button>
                </li>
            </ul>
        </div>

        <!-- Tab Content -->
        <div class="tab-content" id="settingsTabsContent">
            <!-- Profile Tab -->
            <div class="tab-pane fade show active" id="profile" role="tabpanel" aria-labelledby="profile-tab">
                <div class="card settings-card">
                    <div class="settings-card-header">
                        <h3 class="settings-card-title">Informations personnelles</h3>
                        <p class="settings-card-description">Mettez à jour vos informations personnelles</p>
                    </div>
                    <div class="settings-card-body">
                        <form>
                            <div class="row mb-3">
                                <div class="col-md-6 mb-3 mb-md-0">
                                    <label for="firstName" class="form-label">Prénom</label>
                                    <input type="text" class="form-control" id="firstName" value="John">
                                </div>
                                <div class="col-md-6">
                                    <label for="lastName" class="form-label">Nom</label>
                                    <input type="text" class="form-control" id="lastName" value="Doe">
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="email" class="form-label">Email</label>
                                <input type="email" class="form-control" id="email" value="<EMAIL>">
                            </div>
                            <div class="mb-3">
                                <label for="phone" class="form-label">Téléphone</label>
                                <input type="tel" class="form-control" id="phone" value="+221 77 123 4567">
                            </div>
                            <div class="d-flex justify-content-end">
                                <button type="submit" class="btn btn-primary">Enregistrer les modifications</button>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="card settings-card">
                    <div class="settings-card-header">
                        <h3 class="settings-card-title">Photo de profil</h3>
                        <p class="settings-card-description">Mettez à jour votre photo de profil</p>
                    </div>
                    <div class="settings-card-body">
                        <div class="d-flex align-items-center mb-4">
                            <div class="user-avatar" style="width: 80px; height: 80px;">
                                <img src="https://ui-avatars.com/api/?name=John+Doe&background=6366f1&color=fff&size=80" alt="John Doe">
                            </div>
                            <div class="ms-4">
                                <div class="mb-3">
                                    <input class="form-control" type="file" id="profilePicture">
                                </div>
                                <div class="form-text">Formats acceptés : JPG, PNG. Taille maximale : 2 Mo</div>
                            </div>
                        </div>
                        <div class="d-flex justify-content-end">
                            <button type="button" class="btn btn-outline-danger me-2">Supprimer</button>
                            <button type="button" class="btn btn-primary">Télécharger</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Business Tab -->
            <div class="tab-pane fade" id="business" role="tabpanel" aria-labelledby="business-tab">
                <div class="card settings-card">
                    <div class="settings-card-header">
                        <h3 class="settings-card-title">Informations de l'entreprise</h3>
                        <p class="settings-card-description">Mettez à jour les informations de votre entreprise</p>
                    </div>
                    <div class="settings-card-body">
                        <form>
                            <div class="mb-3">
                                <label for="businessName" class="form-label">Nom de l'entreprise</label>
                                <input type="text" class="form-control" id="businessName" value="Acme Inc.">
                            </div>
                            <div class="mb-3">
                                <label for="businessEmail" class="form-label">Email de l'entreprise</label>
                                <input type="email" class="form-control" id="businessEmail" value="<EMAIL>">
                            </div>
                            <div class="mb-3">
                                <label for="businessPhone" class="form-label">Téléphone de l'entreprise</label>
                                <input type="tel" class="form-control" id="businessPhone" value="+221 33 123 4567">
                            </div>
                            <div class="mb-3">
                                <label for="businessAddress" class="form-label">Adresse</label>
                                <textarea class="form-control" id="businessAddress" rows="3">123 Rue Principale, Dakar, Sénégal</textarea>
                            </div>
                            <div class="mb-3">
                                <label for="businessWebsite" class="form-label">Site web</label>
                                <input type="url" class="form-control" id="businessWebsite" value="https://www.acme.com">
                            </div>
                            <div class="d-flex justify-content-end">
                                <button type="submit" class="btn btn-primary">Enregistrer les modifications</button>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="card settings-card">
                    <div class="settings-card-header">
                        <h3 class="settings-card-title">Logo de l'entreprise</h3>
                        <p class="settings-card-description">Mettez à jour le logo de votre entreprise</p>
                    </div>
                    <div class="settings-card-body">
                        <div class="d-flex align-items-center mb-4">
                            <div style="width: 100px; height: 100px; background-color: #f3f4f6; display: flex; align-items: center; justify-content: center; border-radius: var(--border-radius);">
                                <span class="text-gradient" style="font-size: 1.5rem; font-weight: 700;">ACME</span>
                            </div>
                            <div class="ms-4">
                                <div class="mb-3">
                                    <input class="form-control" type="file" id="businessLogo">
                                </div>
                                <div class="form-text">Formats acceptés : JPG, PNG, SVG. Taille maximale : 2 Mo</div>
                            </div>
                        </div>
                        <div class="d-flex justify-content-end">
                            <button type="button" class="btn btn-outline-danger me-2">Supprimer</button>
                            <button type="button" class="btn btn-primary">Télécharger</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Tab -->
            <div class="tab-pane fade" id="payment" role="tabpanel" aria-labelledby="payment-tab">
                <div class="card settings-card">
                    <div class="settings-card-header">
                        <h3 class="settings-card-title">Méthodes de paiement acceptées</h3>
                        <p class="settings-card-description">Configurez les méthodes de paiement que vous souhaitez accepter</p>
                    </div>
                    <div class="settings-card-body">
                        <form>
                            <div class="mb-4">
                                <h5 class="mb-3">Mobile Money</h5>
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="orangeMoney" checked>
                                    <label class="form-check-label" for="orangeMoney">Orange Money</label>
                                </div>
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="mtnMoney" checked>
                                    <label class="form-check-label" for="mtnMoney">MTN Mobile Money</label>
                                </div>
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="moovMoney">
                                    <label class="form-check-label" for="moovMoney">Moov Money</label>
                                </div>
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="waveMoney" checked>
                                    <label class="form-check-label" for="waveMoney">Wave</label>
                                </div>
                            </div>
                            
                            <div class="mb-4">
                                <h5 class="mb-3">Cartes bancaires</h5>
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="visa" checked>
                                    <label class="form-check-label" for="visa">Visa</label>
                                </div>
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="mastercard" checked>
                                    <label class="form-check-label" for="mastercard">Mastercard</label>
                                </div>
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="americanExpress">
                                    <label class="form-check-label" for="americanExpress">American Express</label>
                                </div>
                            </div>
                            
                            <div class="mb-4">
                                <h5 class="mb-3">Autres méthodes</h5>
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="bankTransfer">
                                    <label class="form-check-label" for="bankTransfer">Virement bancaire</label>
                                </div>
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="paypal">
                                    <label class="form-check-label" for="paypal">PayPal</label>
                                </div>
                            </div>
                            
                            <div class="d-flex justify-content-end">
                                <button type="submit" class="btn btn-primary">Enregistrer les modifications</button>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="card settings-card">
                    <div class="settings-card-header">
                        <h3 class="settings-card-title">Devises acceptées</h3>
                        <p class="settings-card-description">Configurez les devises que vous souhaitez accepter</p>
                    </div>
                    <div class="settings-card-body">
                        <form>
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="xof" checked>
                                <label class="form-check-label" for="xof">Franc CFA (XOF)</label>
                            </div>
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="ghs" checked>
                                <label class="form-check-label" for="ghs">Cedi ghanéen (GHS)</label>
                            </div>
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="ngn" checked>
                                <label class="form-check-label" for="ngn">Naira nigérian (NGN)</label>
                            </div>
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="usd">
                                <label class="form-check-label" for="usd">Dollar américain (USD)</label>
                            </div>
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="eur">
                                <label class="form-check-label" for="eur">Euro (EUR)</label>
                            </div>
                            
                            <div class="d-flex justify-content-end">
                                <button type="submit" class="btn btn-primary">Enregistrer les modifications</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Security Tab -->
            <div class="tab-pane fade" id="security" role="tabpanel" aria-labelledby="security-tab">
                <div class="card settings-card">
                    <div class="settings-card-header">
                        <h3 class="settings-card-title">Changer le mot de passe</h3>
                        <p class="settings-card-description">Mettez à jour votre mot de passe pour sécuriser votre compte</p>
                    </div>
                    <div class="settings-card-body">
                        <form>
                            <div class="mb-3">
                                <label for="currentPassword" class="form-label">Mot de passe actuel</label>
                                <input type="password" class="form-control" id="currentPassword">
                            </div>
                            <div class="mb-3">
                                <label for="newPassword" class="form-label">Nouveau mot de passe</label>
                                <input type="password" class="form-control" id="newPassword">
                                <div class="form-text">Le mot de passe doit contenir au moins 8 caractères, une majuscule, une minuscule, un chiffre et un caractère spécial.</div>
                            </div>
                            <div class="mb-3">
                                <label for="confirmPassword" class="form-label">Confirmer le nouveau mot de passe</label>
                                <input type="password" class="form-control" id="confirmPassword">
                            </div>
                            <div class="d-flex justify-content-end">
                                <button type="submit" class="btn btn-primary">Changer le mot de passe</button>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="card settings-card">
                    <div class="settings-card-header">
                        <h3 class="settings-card-title">Authentification à deux facteurs</h3>
                        <p class="settings-card-description">Renforcez la sécurité de votre compte avec l'authentification à deux facteurs</p>
                    </div>
                    <div class="settings-card-body">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <div>
                                <h5 class="mb-1">Authentification par SMS</h5>
                                <p class="text-muted mb-0">Recevez un code de vérification par SMS lors de la connexion</p>
                            </div>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="smsAuth" checked>
                            </div>
                        </div>
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <div>
                                <h5 class="mb-1">Authentification par application</h5>
                                <p class="text-muted mb-0">Utilisez une application d'authentification comme Google Authenticator</p>
                            </div>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="appAuth">
                            </div>
                        </div>
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h5 class="mb-1">Authentification par email</h5>
                                <p class="text-muted mb-0">Recevez un code de vérification par email lors de la connexion</p>
                            </div>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="emailAuth">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card settings-card">
                    <div class="settings-card-header">
                        <h3 class="settings-card-title">Sessions actives</h3>
                        <p class="settings-card-description">Gérez vos sessions de connexion actives</p>
                    </div>
                    <div class="settings-card-body">
                        <div class="d-flex justify-content-between align-items-center mb-4 pb-4 border-bottom">
                            <div>
                                <h5 class="mb-1">Chrome sur Windows</h5>
                                <p class="text-muted mb-0">Dakar, Sénégal · Actif maintenant</p>
                            </div>
                            <div>
                                <span class="badge bg-success me-2">Actuelle</span>
                            </div>
                        </div>
                        <div class="d-flex justify-content-between align-items-center mb-4 pb-4 border-bottom">
                            <div>
                                <h5 class="mb-1">Safari sur iPhone</h5>
                                <p class="text-muted mb-0">Dakar, Sénégal · Dernière activité il y a 2 heures</p>
                            </div>
                            <div>
                                <button class="btn btn-sm btn-outline-danger">Déconnecter</button>
                            </div>
                        </div>
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h5 class="mb-1">Firefox sur MacBook</h5>
                                <p class="text-muted mb-0">Dakar, Sénégal · Dernière activité il y a 5 jours</p>
                            </div>
                            <div>
                                <button class="btn btn-sm btn-outline-danger">Déconnecter</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Notifications Tab -->
            <div class="tab-pane fade" id="notifications" role="tabpanel" aria-labelledby="notifications-tab">
                <div class="card settings-card">
                    <div class="settings-card-header">
                        <h3 class="settings-card-title">Préférences de notification</h3>
                        <p class="settings-card-description">Configurez les notifications que vous souhaitez recevoir</p>
                    </div>
                    <div class="settings-card-body">
                        <form>
                            <div class="mb-4">
                                <h5 class="mb-3">Notifications par email</h5>
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="emailNewPayment" checked>
                                    <label class="form-check-label" for="emailNewPayment">Nouveau paiement reçu</label>
                                </div>
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="emailFailedPayment" checked>
                                    <label class="form-check-label" for="emailFailedPayment">Paiement échoué</label>
                                </div>
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="emailRefund" checked>
                                    <label class="form-check-label" for="emailRefund">Remboursement effectué</label>
                                </div>
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="emailNewCustomer" checked>
                                    <label class="form-check-label" for="emailNewCustomer">Nouveau client</label>
                                </div>
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="emailWeeklySummary" checked>
                                    <label class="form-check-label" for="emailWeeklySummary">Résumé hebdomadaire</label>
                                </div>
                            </div>
                            
                            <div class="mb-4">
                                <h5 class="mb-3">Notifications par SMS</h5>
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="smsNewPayment" checked>
                                    <label class="form-check-label" for="smsNewPayment">Nouveau paiement reçu</label>
                                </div>
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="smsFailedPayment">
                                    <label class="form-check-label" for="smsFailedPayment">Paiement échoué</label>
                                </div>
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="smsLargePayment" checked>
                                    <label class="form-check-label" for="smsLargePayment">Paiement important (>100 000 XOF)</label>
                                </div>
                            </div>
                            
                            <div class="mb-4">
                                <h5 class="mb-3">Notifications dans l'application</h5>
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="appAllNotifications" checked>
                                    <label class="form-check-label" for="appAllNotifications">Toutes les notifications</label>
                                </div>
                            </div>
                            
                            <div class="d-flex justify-content-end">
                                <button type="submit" class="btn btn-primary">Enregistrer les modifications</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- API Tab -->
            <div class="tab-pane fade" id="api" role="tabpanel" aria-labelledby="api-tab">
                <div class="card settings-card">
                    <div class="settings-card-header">
                        <h3 class="settings-card-title">Clés API</h3>
                        <p class="settings-card-description">Gérez vos clés API pour intégrer PaySoft à vos applications</p>
                    </div>
                    <div class="settings-card-body">
                        <div class="alert alert-warning" role="alert">
                            <i class='bx bx-info-circle me-2'></i>
                            Ne partagez jamais vos clés API. Elles donnent accès à votre compte PaySoft.
                        </div>
                        
                        <div class="mb-4">
                            <h5 class="mb-3">Clé API de test</h5>
                            <div class="input-group mb-3">
                                <input type="text" class="form-control" value="pk_test_51JGzT2KJf8n2vOd8X7TkjLJn" readonly>
                                <button class="btn btn-outline-secondary" type="button">
                                    <i class='bx bx-copy'></i>
                                </button>
                            </div>
                            <div class="input-group">
                                <input type="password" class="form-control" value="sk_test_51JGzT2KJf8n2vOd8X7TkjLJn" readonly>
                                <button class="btn btn-outline-secondary" type="button">
                                    <i class='bx bx-show'></i>
                                </button>
                                <button class="btn btn-outline-secondary" type="button">
                                    <i class='bx bx-copy'></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <h5 class="mb-3">Clé API de production</h5>
                            <div class="input-group mb-3">
                                <input type="text" class="form-control" value="pk_live_51JGzT2KJf8n2vOd8X7TkjLJn" readonly>
                                <button class="btn btn-outline-secondary" type="button">
                                    <i class='bx bx-copy'></i>
                                </button>
                            </div>
                            <div class="input-group">
                                <input type="password" class="form-control" value="*********************************" readonly>
                                <button class="btn btn-outline-secondary" type="button">
                                    <i class='bx bx-show'></i>
                                </button>
                                <button class="btn btn-outline-secondary" type="button">
                                    <i class='bx bx-copy'></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-end">
                            <button type="button" class="btn btn-danger me-2">Révoquer les clés</button>
                            <button type="button" class="btn btn-primary">Générer de nouvelles clés</button>
                        </div>
                    </div>
                </div>

                <div class="card settings-card">
                    <div class="settings-card-header">
                        <h3 class="settings-card-title">Webhooks</h3>
                        <p class="settings-card-description">Configurez des webhooks pour recevoir des notifications en temps réel</p>
                    </div>
                    <div class="settings-card-body">
                        <form>
                            <div class="mb-3">
                                <label for="webhookUrl" class="form-label">URL du webhook</label>
                                <input type="url" class="form-control" id="webhookUrl" value="https://www.acme.com/api/paysoft-webhook">
                            </div>
                            <div class="mb-4">
                                <label class="form-label">Événements à notifier</label>
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" id="eventPaymentSuccess" checked>
                                    <label class="form-check-label" for="eventPaymentSuccess">Paiement réussi</label>
                                </div>
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" id="eventPaymentFailed" checked>
                                    <label class="form-check-label" for="eventPaymentFailed">Paiement échoué</label>
                                </div>
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" id="eventRefund" checked>
                                    <label class="form-check-label" for="eventRefund">Remboursement</label>
                                </div>
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" id="eventCustomerCreated">
                                    <label class="form-check-label" for="eventCustomerCreated">Client créé</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="eventSubscriptionUpdated">
                                    <label class="form-check-label" for="eventSubscriptionUpdated">Abonnement mis à jour</label>
                                </div>
                            </div>
                            <div class="d-flex justify-content-end">
                                <button type="button" class="btn btn-outline-primary me-2">Tester le webhook</button>
                                <button type="submit" class="btn btn-primary">Enregistrer</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Toggle Sidebar
        const sidebar = document.getElementById('sidebar');
        const sidebarToggle = document.getElementById('sidebarToggle');
        const sidebarClose = document.getElementById('sidebarClose');
        const sidebarOverlay = document.getElementById('sidebarOverlay');

        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', () => {
                sidebar.classList.toggle('show');
                sidebarOverlay.classList.toggle('show');
            });
        }

        if (sidebarClose) {
            sidebarClose.addEventListener('click', () => {
                sidebar.classList.remove('show');
                sidebarOverlay.classList.remove('show');
            });
        }

        if (sidebarOverlay) {
            sidebarOverlay.addEventListener('click', () => {
                sidebar.classList.remove('show');
                sidebarOverlay.classList.remove('show');
            });
        }

        // Show/Hide Password
        const passwordToggles = document.querySelectorAll('.bx-show');
        passwordToggles.forEach(toggle => {
            toggle.addEventListener('click', (e) => {
                const button = e.currentTarget;
                const input = button.parentElement.previousElementSibling;
                
                if (input.type === 'password') {
                    input.type = 'text';
                    button.classList.remove('bx-show');
                    button.classList.add('bx-hide');
                } else {
                    input.type = 'password';
                    button.classList.remove('bx-hide');
                    button.classList.add('bx-show');
                }
            });
        });

        // Copy to Clipboard
        const copyButtons = document.querySelectorAll('.bx-copy');
        copyButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const input = e.currentTarget.parentElement.previousElementSibling;
                input.select();
                document.execCommand('copy');
                
                // Show copied notification
                const originalHTML = e.currentTarget.innerHTML;
                e.currentTarget.innerHTML = '<i class="bx bx-check"></i>';
                setTimeout(() => {
                    e.currentTarget.innerHTML = originalHTML;
                }, 2000);
            });
        });
    </script>
</body>
</html>